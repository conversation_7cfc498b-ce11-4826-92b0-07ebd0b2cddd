<?php
/**
 * Тест админ-панели AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест админ-панели AstroGenix</h1>";

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Тест SQL-запросов админ-панели</h2>";

// Тест 1: Общее количество пользователей
try {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $total_users = $stmt->fetch()['count'];
    echo "<p>✅ Общее количество пользователей: <strong>$total_users</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения пользователей: " . $e->getMessage() . "</p>";
}

// Тест 2: Майнинг-пакеты (вместо старых инвестиций)
try {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM mining_packages WHERE is_active = 1");
    $stmt->execute();
    $total_packages = $stmt->fetch()['count'];
    echo "<p>✅ Активных майнинг-пакетов: <strong>$total_packages</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения майнинг-пакетов: " . $e->getMessage() . "</p>";
}

// Тест 3: Общая сумма инвестиций
try {
    $stmt = $db->prepare("SELECT SUM(investment_amount) as total FROM user_mining_investments WHERE is_active = 1");
    $stmt->execute();
    $total_invested = $stmt->fetch()['total'] ?? 0;
    echo "<p>✅ Общая сумма инвестиций: <strong>" . formatCurrency($total_invested) . "</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения суммы инвестиций: " . $e->getMessage() . "</p>";
}

// Тест 4: Ожидающие транзакции
try {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'");
    $stmt->execute();
    $pending_transactions = $stmt->fetch()['count'];
    echo "<p>✅ Ожидающих транзакций: <strong>$pending_transactions</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения транзакций: " . $e->getMessage() . "</p>";
}

// Тест 5: Недавние майнинг-инвестиции
try {
    $stmt = $db->prepare("
        SELECT 'mining_investment' as type, umi.created_at, u.username, mp.name as title, umi.investment_amount as amount
        FROM user_mining_investments umi
        JOIN users u ON umi.user_id = u.id
        JOIN mining_packages mp ON umi.package_id = mp.id
        ORDER BY umi.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_investments = $stmt->fetchAll();
    echo "<p>✅ Недавних майнинг-инвестиций: <strong>" . count($recent_investments) . "</strong></p>";
    
    if (!empty($recent_investments)) {
        echo "<ul>";
        foreach ($recent_investments as $investment) {
            echo "<li>" . htmlspecialchars($investment['username']) . " → " . htmlspecialchars($investment['title']) . " (" . formatCurrency($investment['amount']) . ")</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения недавних инвестиций: " . $e->getMessage() . "</p>";
}

// Тест 6: Недавние транзакции
try {
    $stmt = $db->prepare("
        SELECT 'transaction' as type, t.created_at, u.username, t.type, t.amount, t.status
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_transactions = $stmt->fetchAll();
    echo "<p>✅ Недавних транзакций: <strong>" . count($recent_transactions) . "</strong></p>";
    
    if (!empty($recent_transactions)) {
        echo "<ul>";
        foreach ($recent_transactions as $transaction) {
            echo "<li>" . htmlspecialchars($transaction['username']) . " → " . ucfirst($transaction['type']) . " (" . formatCurrency($transaction['amount']) . ") - " . ucfirst($transaction['status']) . "</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения недавних транзакций: " . $e->getMessage() . "</p>";
}

// Тест 7: Проверка существования админ-страниц
echo "<h2>Проверка админ-страниц</h2>";

$admin_pages = [
    'admin/index.php' => 'Главная админ-панели',
    'admin/mining_packages.php' => 'Управление майнинг-пакетами',
    'admin/eco_news.php' => 'Управление новостями',
    'admin/referral_system.php' => 'Реферальная система',
    'admin/users.php' => 'Управление пользователями',
    'admin/transactions.php' => 'Управление транзакциями'
];

foreach ($admin_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>⚠️ $description ($page) не найден</p>";
    }
}

// Тест 8: Проверка функций времени
echo "<h2>Проверка вспомогательных функций</h2>";

if (function_exists('timeAgo')) {
    echo "<p>✅ Функция timeAgo() доступна</p>";
    echo "<p>Пример: " . timeAgo(date('Y-m-d H:i:s', strtotime('-2 hours'))) . "</p>";
} else {
    echo "<p>❌ Функция timeAgo() не найдена</p>";
}

if (function_exists('formatCurrency')) {
    echo "<p>✅ Функция formatCurrency() доступна</p>";
    echo "<p>Пример: " . formatCurrency(1234.56) . "</p>";
} else {
    echo "<p>❌ Функция formatCurrency() не найдена</p>";
}

// Итоговый результат
echo "<h2>🎯 Результат тестирования админ-панели</h2>";

$admin_ready = true;

// Проверяем основные условия
if (!isset($total_users) || !isset($total_packages)) {
    $admin_ready = false;
}

if ($admin_ready) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Админ-панель готова к работе!</h3>";
    echo "<p><strong>Статистика системы:</strong></p>";
    echo "<ul>";
    echo "<li>👥 Пользователей: $total_users</li>";
    echo "<li>⚡ Майнинг-пакетов: $total_packages</li>";
    echo "<li>💰 Общие инвестиции: " . formatCurrency($total_invested ?? 0) . "</li>";
    echo "<li>⏳ Ожидающих транзакций: " . ($pending_transactions ?? 0) . "</li>";
    echo "</ul>";
    echo "<p><strong>Доступные разделы:</strong></p>";
    echo "<ul>";
    echo "<li><a href='admin/index.php' target='_blank'>🏠 Главная админ-панели</a></li>";
    echo "<li><a href='admin/mining_packages.php' target='_blank'>⚡ Майнинг-пакеты</a></li>";
    echo "<li><a href='admin/eco_news.php' target='_blank'>📰 Экологические новости</a></li>";
    echo "<li><a href='admin/referral_system.php' target='_blank'>👥 Реферальная система</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Требуется настройка</h3>";
    echo "<p>Обнаружены проблемы с админ-панелью:</p>";
    echo "<ul>";
    echo "<li>Импортируйте полную схему БД из database/astrogenix_eco_mining.sql</li>";
    echo "<li>Создайте администратора: UPDATE users SET role = 'admin' WHERE id = 1;</li>";
    echo "<li>Проверьте права доступа к файлам</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . "</small></p>";
?>
