<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../pages/login.php');
    exit;
}

$page_title = 'Referral System Management';

// Get referral statistics
$referral_stats = getReferralStatistics();
$top_referrers = getReferralRankings(10);
$recent_referrals = getRecentReferrals(20);

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Referral System Management</h1>
            <p class="text-gray-600 mt-2">Statistics and management of the referral program</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Referrals</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($referral_stats['total_referrals'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Referrers</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($referral_stats['active_referrers'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-coins text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Payouts</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo formatCurrency($referral_stats['total_earnings'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Conversion</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($referral_stats['conversion_rate'] ?? 0, 1); ?>%</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Top Referrers -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Top Referrers</h3>
                </div>
                <div class="p-6">
                    <?php if (!empty($top_referrers)): ?>
                        <div class="space-y-4">
                            <?php foreach ($top_referrers as $index => $referrer): ?>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full <?php echo $index < 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gray-300'; ?> text-white font-bold">
                                            <?php if ($index === 0): ?>
                                                <i class="fas fa-crown"></i>
                                            <?php elseif ($index === 1): ?>
                                                <i class="fas fa-medal"></i>
                                            <?php elseif ($index === 2): ?>
                                                <i class="fas fa-award"></i>
                                            <?php else: ?>
                                                <?php echo $index + 1; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($referrer['username']); ?></p>
                                            <p class="text-sm text-gray-500"><?php echo $referrer['total_referrals']; ?> referrals</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900"><?php echo formatCurrency($referrer['total_referral_earnings']); ?></p>
                                        <p class="text-sm text-gray-500">earned</p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">No referral data</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Referrals -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Referrals</h3>
                </div>
                <div class="p-6">
                    <?php if (!empty($recent_referrals)): ?>
                        <div class="space-y-4">
                            <?php foreach ($recent_referrals as $referral): ?>
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($referral['referred_username']); ?>
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            Invited by: <?php echo htmlspecialchars($referral['referrer_username']); ?>
                                        </p>
                                        <p class="text-xs text-gray-400">
                                            <?php echo date('d.m.Y H:i', strtotime($referral['created_at'])); ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $referral['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $referral['status'] === 'active' ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">Нет недавних рефералов</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Referral Settings -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Настройки реферальной системы</h3>
            </div>
            <div class="p-6">
                <form method="POST" action="update_referral_settings.php">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Комиссия за регистрацию (USDT)
                            </label>
                            <input type="number" 
                                   name="signup_bonus" 
                                   step="0.01" 
                                   value="<?php echo getSiteSetting('referral_signup_bonus', '5.00'); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Комиссия с инвестиций (%)
                            </label>
                            <input type="number" 
                                   name="investment_commission" 
                                   step="0.01" 
                                   value="<?php echo getSiteSetting('referral_investment_commission', '5.00'); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Минимальная выплата (USDT)
                            </label>
                            <input type="number" 
                                   name="min_withdrawal" 
                                   step="0.01" 
                                   value="<?php echo getSiteSetting('referral_min_withdrawal', '10.00'); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Статус системы
                            </label>
                            <select name="referral_enabled" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="1" <?php echo getSiteSetting('referral_enabled', '1') ? 'selected' : ''; ?>>Включена</option>
                                <option value="0" <?php echo !getSiteSetting('referral_enabled', '1') ? 'selected' : ''; ?>>Отключена</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            Сохранить настройки
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Referral Tools -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Инструменты администратора</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="simulateReferrals()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                        <i class="fas fa-users mr-2"></i>
                        Симулировать рефералы
                    </button>
                    
                    <button onclick="exportReferralData()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Экспорт данных
                    </button>
                    
                    <button onclick="resetReferralStats()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                        <i class="fas fa-refresh mr-2"></i>
                        Сброс статистики
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function simulateReferrals() {
    if (confirm('Создать тестовые реферальные связи?')) {
        fetch('simulate_referrals.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error.message);
        });
    }
}

function exportReferralData() {
    window.open('export_referral_data.php', '_blank');
}

function resetReferralStats() {
    if (confirm('Вы уверены, что хотите сбросить всю статистику рефералов? Это действие нельзя отменить.')) {
        fetch('reset_referral_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            alert('Ошибка: ' + error.message);
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
