<?php
/**
 * AstroGenix Database Setup Script
 * Автоматическое создание базы данных для локальной разработки
 */

// Настройки для локального сервера (OSPanel)
$host = 'localhost';
$username = 'root';
$password = '';
$database_name = 'astrogenix_eco_mining';

echo "<h1>🚀 Настройка базы данных AstroGenix</h1>";

try {
    // Подключение к MySQL без указания базы данных
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Подключение к MySQL успешно</p>";
    
    // Создание базы данных
    $sql = "CREATE DATABASE IF NOT EXISTS `$database_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    
    echo "<p>✅ База данных '$database_name' создана</p>";
    
    // Подключение к созданной базе данных
    $pdo = new PDO("mysql:host=$host;dbname=$database_name", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Подключение к базе данных '$database_name' успешно</p>";
    
    // Проверка существования таблиц
    $tables = [
        'users', 'mining_packages', 'user_mining_investments', 
        'daily_mining_profits', 'referrals', 'referral_earnings',
        'mining_rankings', 'referral_rankings', 'eco_news', 'site_settings'
    ];
    
    $existing_tables = [];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            $existing_tables[] = $table;
        }
    }
    
    if (count($existing_tables) > 0) {
        echo "<p>⚠️ Найдены существующие таблицы: " . implode(', ', $existing_tables) . "</p>";
        echo "<p>📋 Для полной установки импортируйте файл database/astrogenix_eco_mining.sql</p>";
    } else {
        echo "<p>📋 База данных пуста. Импортируйте файл database/astrogenix_eco_mining.sql</p>";
    }
    
    // Создание базовых таблиц если их нет
    if (empty($existing_tables)) {
        echo "<p>🔧 Создание базовых таблиц...</p>";
        
        // Создание таблицы пользователей
        $sql = "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password_hash` varchar(255) NOT NULL,
            `role` enum('user','admin') DEFAULT 'user',
            `referral_code` varchar(20) UNIQUE,
            `referred_by` int(11) DEFAULT NULL,
            `balance` decimal(10,2) DEFAULT 0.00,
            `total_invested` decimal(10,2) DEFAULT 0.00,
            `total_earned` decimal(10,2) DEFAULT 0.00,
            `verification_status` enum('unverified','pending','verified','rejected') DEFAULT 'unverified',
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`),
            KEY `referred_by` (`referred_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p>✅ Таблица users создана</p>";
        
        // Создание таблицы майнинг-пакетов
        $sql = "
        CREATE TABLE IF NOT EXISTS `mining_packages` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `description` text,
            `energy_type` enum('solar','wind','hydro','geothermal','hybrid','biomass') NOT NULL,
            `min_investment` decimal(10,2) NOT NULL,
            `max_investment` decimal(10,2) DEFAULT NULL,
            `daily_rate` decimal(6,4) NOT NULL,
            `duration_days` int(11) NOT NULL,
            `hash_power` varchar(50),
            `energy_consumption` decimal(8,2),
            `co2_saved` decimal(8,2),
            `location` varchar(100),
            `image_url` varchar(255),
            `features` json,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p>✅ Таблица mining_packages создана</p>";
        
        // Создание таблицы настроек сайта
        $sql = "
        CREATE TABLE IF NOT EXISTS `site_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL,
            `setting_value` text,
            `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p>✅ Таблица site_settings создана</p>";
        
        // Вставка базовых настроек
        $settings = [
            ['site_name', 'AstroGenix', 'text'],
            ['site_description', 'Eco Mining Platform for Sustainable Cryptocurrency Mining', 'text'],
            ['contact_email', '<EMAIL>', 'text'],
            ['usdt_wallet_address', 'TRC20WalletAddressHere', 'text'],
            ['referral_enabled', '1', 'boolean'],
            ['referral_signup_bonus', '5.00', 'number'],
            ['referral_investment_commission', '5.00', 'number']
        ];
        
        foreach ($settings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
        echo "<p>✅ Базовые настройки добавлены</p>";
    }
    
    echo "<h2>🎉 Настройка завершена!</h2>";
    echo "<p><strong>Следующие шаги:</strong></p>";
    echo "<ol>";
    echo "<li>Импортируйте полную схему из файла <code>database/astrogenix_eco_mining.sql</code></li>";
    echo "<li>Зарегистрируйте первого пользователя через веб-интерфейс</li>";
    echo "<li>Сделайте его администратором через SQL: <code>UPDATE users SET role = 'admin' WHERE id = 1;</code></li>";
    echo "<li>Перейдите на <a href='index.php'>главную страницу</a></li>";
    echo "</ol>";
    
} catch(PDOException $e) {
    echo "<p>❌ Ошибка: " . $e->getMessage() . "</p>";
    echo "<p>Проверьте настройки подключения к базе данных в файле config/database.php</p>";
}
?>
