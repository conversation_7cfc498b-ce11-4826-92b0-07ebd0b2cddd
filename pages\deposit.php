<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

requireLogin();
$user_id = getCurrentUserId();
$user = getUserById($user_id);

// Check if KY<PERSON> is required and user is verified
$kyc_required = isKYCRequired();
$user_verified = isUserVerified($user_id);
$can_make_transactions = canUserMakeTransactions($user_id);

$page_title = 'Deposit Funds';
$page_description = 'Make a deposit to your Poseidon investment account';

// Handle deposit request
$success_message = null;
$error_message = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'deposit') {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        // Check if user can make transactions
        if (!$can_make_transactions) {
            $error_message = 'Account verification is required to make deposits. Please complete your KYC verification first.';
        } else {
            $amount = floatval($_POST['amount'] ?? 0);
            $screenshot_file = $_FILES['transaction_screenshot'] ?? null;

        // Enhanced debug information
        error_log("=== DEPOSIT PAGE DEBUG ===");
        error_log("Amount: $amount");
        error_log("File: " . print_r($screenshot_file, true));
        error_log("User ID: $user_id");
        error_log("POST data: " . print_r($_POST, true));
        error_log("FILES data: " . print_r($_FILES, true));

        if (isset($_FILES['transaction_screenshot'])) {
            $file = $_FILES['transaction_screenshot'];
            error_log("File upload details:");
            error_log("- Name: " . ($file['name'] ?? 'N/A'));
            error_log("- Size: " . ($file['size'] ?? 'N/A'));
            error_log("- Type: " . ($file['type'] ?? 'N/A'));
            error_log("- Error: " . ($file['error'] ?? 'N/A'));
            error_log("- Tmp name: " . ($file['tmp_name'] ?? 'N/A'));
            error_log("- Is uploaded file: " . (is_uploaded_file($file['tmp_name'] ?? '') ? 'YES' : 'NO'));
        } else {
            error_log("No file in \$_FILES['transaction_screenshot']");
        }

        // Validate file before processing
        if (!$screenshot_file || !isset($screenshot_file['tmp_name']) || $screenshot_file['error'] !== 0) {
            $error_message = 'Please select a valid transaction screenshot file.';
            error_log("File validation failed: " . ($screenshot_file['error'] ?? 'No file'));
        } else {
            $result = processDepositRequest($user_id, $amount, $screenshot_file);

            error_log("processDepositRequest result: " . print_r($result, true));
            error_log("=== END DEBUG ===");

            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
        }
        } // End of KYC check
    } else {
        $error_message = 'Invalid security token. Please try again.';
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-xl shadow-lg animate-fade-in">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3 text-xl"></i>
                    <div>
                        <h4 class="font-bold text-green-900">Success!</h4>
                        <p><?php echo htmlspecialchars($success_message); ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-lg animate-fade-in">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>
                    <div>
                        <h4 class="font-bold text-red-900">Error</h4>
                        <p><?php echo htmlspecialchars($error_message); ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-4 shadow-lg">
                <i class="fas fa-plus text-white text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-2">Deposit Funds</h1>
            <p class="text-xl text-gray-600">Add funds to your investment account</p>
        </div>

        <!-- KYC Warning -->
        <?php if ($kyc_required && !$user_verified): ?>
        <div class="mb-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl mr-4 mt-1"></i>
                <div>
                    <h3 class="text-lg font-bold text-yellow-800 mb-2">Account Verification Required</h3>
                    <p class="text-yellow-700 mb-4">
                        To make deposits, you need to complete your account verification (KYC). This helps us ensure the security of your account and comply with regulations.
                    </p>
                    <a href="verification.php" class="inline-flex items-center bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-id-card mr-2"></i>
                        Complete Verification
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Deposit Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                    <!-- Wallet Information Section -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-wallet mr-3"></i>
                            TRC-20 USDT Deposit Address
                        </h3>
                        
                        <div class="bg-white bg-opacity-20 rounded-xl p-4 mb-4">
                            <p class="text-sm text-blue-100 mb-2">Send USDT to this address:</p>
                            <div class="flex items-center space-x-3">
                                <code id="walletAddress" class="flex-1 bg-white bg-opacity-30 text-white font-mono text-sm p-3 rounded-lg break-all">
                                    <?php echo getSiteSetting('usdt_wallet_address', 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE'); ?>
                                </code>
                                <button onclick="copyWalletAddress()" class="bg-white bg-opacity-30 hover:bg-opacity-50 text-white px-4 py-3 rounded-lg transition-colors" title="Copy Address">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Network</p>
                                <p class="text-blue-100">TRC-20 (Tron)</p>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Minimum</p>
                                <p class="text-blue-100">$10 USDT</p>
                            </div>
                        </div>
                    </div>

                    <!-- Deposit Form -->
                    <div class="p-6">
                        <form method="POST" enctype="multipart/form-data" class="space-y-6" onsubmit="return validateDepositForm(this)">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="deposit">

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-dollar-sign text-blue-500 mr-2"></i>
                                    Deposit Amount (USDT)
                                </label>
                                <input type="number" name="amount" step="0.01" min="10" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                                       placeholder="Enter amount (minimum $10)">
                                <p class="text-sm text-gray-500 mt-2">Minimum deposit amount: $10 USDT</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-camera text-blue-500 mr-2"></i>
                                    Transaction Screenshot
                                </label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors bg-gray-50">
                                    <input type="file" name="transaction_screenshot" id="screenshotInput" accept="image/*" required
                                           class="hidden" onchange="handleFileSelect(this)">
                                    <label for="screenshotInput" class="cursor-pointer">
                                        <div id="uploadArea">
                                            <i class="fas fa-cloud-upload-alt text-gray-400 text-4xl mb-4"></i>
                                            <p class="text-gray-600 text-lg mb-2">Click to upload transaction screenshot</p>
                                            <p class="text-sm text-gray-500">JPG, PNG, GIF up to 5MB</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <div id="filePreview" class="mt-4 hidden">
                                    <div class="flex items-center space-x-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                                        <i class="fas fa-file-image text-blue-500 text-2xl"></i>
                                        <div class="flex-1">
                                            <p id="fileName" class="font-medium text-gray-900"></p>
                                            <p id="fileSize" class="text-sm text-gray-500"></p>
                                        </div>
                                        <button type="button" onclick="removeFile()" class="text-red-500 hover:text-red-700 p-2">
                                            <i class="fas fa-times text-lg"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <p class="text-sm text-gray-500 mt-2">
                                    Upload a screenshot of your USDT transaction for verification
                                </p>
                            </div>

                            <div class="flex space-x-4">
                                <a href="dashboard.php" class="flex-1 border-2 border-gray-300 text-gray-700 py-4 rounded-xl hover:bg-gray-50 transition-colors font-medium text-center">
                                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-4 rounded-xl font-medium transition-all duration-300 shadow-lg transform hover:scale-105">
                                    <i class="fas fa-paper-plane mr-2"></i>Submit Deposit Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Instructions -->
                <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        How to Deposit
                    </h3>
                    <ol class="space-y-3 text-sm text-gray-600">
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                            <span>Send USDT to the wallet address above using TRC-20 network</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                            <span>Take a screenshot of the completed transaction</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                            <span>Upload the screenshot and enter the amount</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
                            <span>Submit your deposit request for approval</span>
                        </li>
                    </ol>
                </div>

                <!-- Important Notes -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
                    <h3 class="text-lg font-bold text-yellow-900 mb-4 flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                        Important Notes
                    </h3>
                    <ul class="space-y-2 text-sm text-yellow-800">
                        <li class="flex items-start">
                            <i class="fas fa-check text-yellow-600 mr-2 mt-1"></i>
                            <span>Only send TRC-20 USDT to this address</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-yellow-600 mr-2 mt-1"></i>
                            <span>Minimum deposit amount is $10</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-yellow-600 mr-2 mt-1"></i>
                            <span>Processing time: up to 24 hours</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-yellow-600 mr-2 mt-1"></i>
                            <span>Keep your transaction hash for reference</span>
                        </li>
                    </ul>
                </div>

                <!-- Account Balance -->
                <div class="bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl p-6 text-white">
                    <h3 class="text-lg font-bold mb-4 flex items-center">
                        <i class="fas fa-wallet mr-2"></i>
                        Current Balance
                    </h3>
                    <p class="text-3xl font-bold mb-2"><?php echo formatCurrency($user['balance'] ?? 0); ?></p>
                    <p class="text-green-100 text-sm">Available for investment</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
function validateDepositForm(form) {
    const fileInput = form.querySelector('input[name="transaction_screenshot"]');
    const amountInput = form.querySelector('input[name="amount"]');

    console.log('Form validation - File input:', fileInput);
    console.log('Form validation - Files:', fileInput ? fileInput.files : 'No file input');
    console.log('Form validation - Amount:', amountInput ? amountInput.value : 'No amount input');

    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        alert('Please select a transaction screenshot');
        return false;
    }

    const file = fileInput.files[0];
    console.log('Form validation - Selected file:', file);

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('Please select a valid image file (JPG, PNG, or GIF)');
        return false;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return false;
    }

    return true;
}

// File handling functions
function handleFileSelect(input) {
    const file = input.files[0];
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, or GIF)');
            input.value = '';
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            input.value = '';
            return;
        }

        // Show file preview
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        filePreview.classList.remove('hidden');
    }
}

function removeFile() {
    document.getElementById('screenshotInput').value = '';
    document.getElementById('filePreview').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Copy wallet address - WORKING VERSION WITH FALLBACK
function copyWalletAddress() {
    const walletAddress = document.getElementById('walletAddress').textContent.trim();
    console.log('Copying wallet address:', walletAddress);

    // Use modern clipboard API with fallback
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(walletAddress).then(function() {
            console.log('Clipboard API copy successful');
            showCopySuccess();
        }).catch(function(err) {
            console.log('Clipboard API failed, using fallback:', err);
            fallbackCopyTextToClipboard(walletAddress);
        });
    } else {
        console.log('Using fallback copy method');
        fallbackCopyTextToClipboard(walletAddress);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('Fallback copy result:', successful);
        if (successful) {
            showCopySuccess();
        } else {
            alert('Failed to copy address. Please copy manually: ' + text);
        }
    } catch (err) {
        console.log('Fallback copy error:', err);
        alert('Failed to copy address. Please copy manually: ' + text);
    }

    document.body.removeChild(textArea);
}

function showCopySuccess() {
    // Find the button that was clicked
    const buttons = document.querySelectorAll('button[onclick="copyWalletAddress()"]');
    let button = null;

    // Try to get the button from the event
    if (window.event && window.event.target) {
        button = window.event.target.closest('button');
    }

    // Fallback to first button if event target not found
    if (!button && buttons.length > 0) {
        button = buttons[0];
    }

    if (button) {
        const originalHTML = button.innerHTML;
        const originalClasses = button.className;

        button.innerHTML = '<i class="fas fa-check"></i>';
        button.className = 'bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg transition-colors';

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.className = originalClasses;
        }, 2000);
    }

    // Also show a temporary notification
    const notification = document.createElement('div');
    notification.innerHTML = '<i class="fas fa-check mr-2"></i>Address copied to clipboard!';
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    document.body.appendChild(notification);

    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Auto-hide success/error messages
setTimeout(function() {
    const alerts = document.querySelectorAll('.animate-fade-in');
    alerts.forEach(function(alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(function() {
            alert.remove();
        }, 300);
    });
}, 5000);
</script>

<?php include '../includes/footer.php'; ?>
