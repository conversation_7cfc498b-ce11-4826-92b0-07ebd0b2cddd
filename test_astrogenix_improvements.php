<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h1>🚀 AstroGenix Platform Improvements Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #10b981; }
    .error { color: #ef4444; }
    .warning { color: #f59e0b; }
    .info { color: #3b82f6; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; }
</style>";

$all_tests_passed = true;

// Test 1: Referral Code Field in Registration
echo "<div class='test-section'>";
echo "<h2>1. 📝 Referral Code Field in Registration</h2>";

if (file_exists('pages/register.php')) {
    $register_content = file_get_contents('pages/register.php');
    
    if (strpos($register_content, 'name="referral_code"') !== false) {
        echo "<p class='success'>✅ Referral code input field added to registration form</p>";
    } else {
        echo "<p class='error'>❌ Referral code field not found in registration form</p>";
        $all_tests_passed = false;
    }
    
    if (strpos($register_content, 'Referral Code') !== false) {
        echo "<p class='success'>✅ Referral code label found</p>";
    } else {
        echo "<p class='error'>❌ Referral code label not found</p>";
        $all_tests_passed = false;
    }
    
    if (strpos($register_content, 'Get bonus rewards') !== false) {
        echo "<p class='success'>✅ Referral bonus message added</p>";
    } else {
        echo "<p class='warning'>⚠️ Referral bonus message not found</p>";
    }
} else {
    echo "<p class='error'>❌ Registration page not found</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 2: KYC Settings in Admin Panel
echo "<div class='test-section'>";
echo "<h2>2. ⚙️ KYC Settings in Admin Panel</h2>";

if (file_exists('admin/settings.php')) {
    $settings_content = file_get_contents('admin/settings.php');
    
    if (strpos($settings_content, 'kyc_verification_required') !== false) {
        echo "<p class='success'>✅ KYC setting field added to admin settings</p>";
    } else {
        echo "<p class='error'>❌ KYC setting field not found in admin settings</p>";
        $all_tests_passed = false;
    }
    
    if (strpos($settings_content, 'Требовать KYC верификацию') !== false) {
        echo "<p class='success'>✅ KYC setting label found</p>";
    } else {
        echo "<p class='error'>❌ KYC setting label not found</p>";
        $all_tests_passed = false;
    }
} else {
    echo "<p class='error'>❌ Admin settings page not found</p>";
    $all_tests_passed = false;
}

// Test KYC functions
try {
    if (function_exists('isKYCRequired')) {
        echo "<p class='success'>✅ isKYCRequired() function exists</p>";
    } else {
        echo "<p class='error'>❌ isKYCRequired() function not found</p>";
        $all_tests_passed = false;
    }
    
    if (function_exists('canUserMakeTransactions')) {
        echo "<p class='success'>✅ canUserMakeTransactions() function exists</p>";
    } else {
        echo "<p class='error'>❌ canUserMakeTransactions() function not found</p>";
        $all_tests_passed = false;
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error testing KYC functions: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 3: KYC Integration in Transaction Pages
echo "<div class='test-section'>";
echo "<h2>3. 🔒 KYC Integration in Transaction Pages</h2>";

$transaction_pages = ['pages/deposit.php', 'pages/withdrawal.php'];
foreach ($transaction_pages as $page) {
    if (file_exists($page)) {
        $page_content = file_get_contents($page);
        
        if (strpos($page_content, 'canUserMakeTransactions') !== false) {
            echo "<p class='success'>✅ KYC check added to " . basename($page) . "</p>";
        } else {
            echo "<p class='error'>❌ KYC check not found in " . basename($page) . "</p>";
            $all_tests_passed = false;
        }
        
        if (strpos($page_content, 'Account verification is required') !== false) {
            echo "<p class='success'>✅ KYC warning message found in " . basename($page) . "</p>";
        } else {
            echo "<p class='warning'>⚠️ KYC warning message not found in " . basename($page) . "</p>";
        }
    } else {
        echo "<p class='error'>❌ " . basename($page) . " not found</p>";
        $all_tests_passed = false;
    }
}
echo "</div>";

// Test 4: English Translation Progress
echo "<div class='test-section'>";
echo "<h2>4. 🌐 English Translation Progress</h2>";

if (file_exists('pages/referral.php')) {
    $referral_content = file_get_contents('pages/referral.php');
    
    $english_terms = [
        'Referral Program',
        'Invite Friends',
        'Total Referrals',
        'Active Referrals',
        'Total Earned',
        'Your Referral Link'
    ];
    
    $found_terms = 0;
    foreach ($english_terms as $term) {
        if (strpos($referral_content, $term) !== false) {
            $found_terms++;
        }
    }
    
    $percentage = round(($found_terms / count($english_terms)) * 100);
    
    if ($percentage >= 80) {
        echo "<p class='success'>✅ Referral page translation: {$percentage}% complete ({$found_terms}/" . count($english_terms) . " terms)</p>";
    } elseif ($percentage >= 50) {
        echo "<p class='warning'>⚠️ Referral page translation: {$percentage}% complete ({$found_terms}/" . count($english_terms) . " terms)</p>";
    } else {
        echo "<p class='error'>❌ Referral page translation: {$percentage}% complete ({$found_terms}/" . count($english_terms) . " terms)</p>";
        $all_tests_passed = false;
    }
} else {
    echo "<p class='error'>❌ Referral page not found</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 5: Database Settings
echo "<div class='test-section'>";
echo "<h2>5. 🗄️ Database Settings</h2>";

try {
    $db = getDB();
    
    // Check if KYC setting exists in database
    $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = 'kyc_verification_required'");
    $stmt->execute();
    $kyc_setting = $stmt->fetch();
    
    if ($kyc_setting) {
        echo "<p class='success'>✅ KYC setting found in database (value: " . ($kyc_setting['setting_value'] ?? 'NULL') . ")</p>";
    } else {
        echo "<p class='warning'>⚠️ KYC setting not found in database (will be created on first save)</p>";
    }
    
    // Check blog_posts table
    $stmt = $db->prepare("SHOW TABLES LIKE 'blog_posts'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<p class='success'>✅ blog_posts table exists</p>";
    } else {
        echo "<p class='error'>❌ blog_posts table missing</p>";
        $all_tests_passed = false;
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 6: File Integrity
echo "<div class='test-section'>";
echo "<h2>6. 📁 File Integrity Check</h2>";

$critical_files = [
    'pages/register.php' => 'Registration page',
    'pages/deposit.php' => 'Deposit page',
    'pages/withdrawal.php' => 'Withdrawal page',
    'pages/referral.php' => 'Referral page',
    'admin/settings.php' => 'Admin settings',
    'includes/functions.php' => 'Core functions'
];

foreach ($critical_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ {$description} exists</p>";
    } else {
        echo "<p class='error'>❌ {$description} missing</p>";
        $all_tests_passed = false;
    }
}
echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>📊 Test Summary</h2>";

if ($all_tests_passed) {
    echo "<p class='success'><strong>🎉 All critical tests passed!</strong></p>";
    echo "<p class='info'>The AstroGenix platform improvements have been successfully implemented.</p>";
} else {
    echo "<p class='error'><strong>⚠️ Some tests failed.</strong></p>";
    echo "<p class='warning'>Please review the failed tests above and run the fix_database_integrity.php script if needed.</p>";
}

echo "<h3>🔧 Next Steps:</h3>";
echo "<ul>";
echo "<li>Run <code>fix_database_integrity.php</code> to ensure all database tables and settings are created</li>";
echo "<li>Test the referral code field in registration</li>";
echo "<li>Test KYC settings in admin panel</li>";
echo "<li>Complete remaining translations for user dashboard pages</li>";
echo "<li>Remove any remaining Poseidon branding references</li>";
echo "</ul>";
echo "</div>";
?>
