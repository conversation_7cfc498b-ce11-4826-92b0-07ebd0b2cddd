<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$user_id = intval($_GET['id'] ?? 0);
if (!$user_id) {
    redirect('users.php');
}

$page_title = 'Детали пользователя';

// Get detailed user statistics
$user_stats = getUserDetailedStats($user_id);
if (empty($user_stats)) {
    setFlashMessage('error', 'Пользователь не найден');
    redirect('users.php');
}

$user = $user_stats['user'];
$investments = $user_stats['investments'];
$transactions = $user_stats['transactions'];
$referrals = $user_stats['referrals'];
$recent_activity = $user_stats['recent_activity'];

include '../includes/admin_header.php';
?>

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Детали пользователя</h1>
                <p class="text-gray-600 mt-2">Подробная информация о пользователе <?php echo htmlspecialchars($user['username']); ?></p>
            </div>
            <a href="users.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Назад к списку
            </a>
        </div>

        <!-- User Info Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Основная информация</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ID пользователя</label>
                        <p class="text-lg font-semibold text-gray-900">#<?php echo $user['id']; ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Имя пользователя</label>
                        <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($user['username']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($user['email']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Баланс</label>
                        <p class="text-lg font-semibold text-green-600"><?php echo formatCurrency($user['balance']); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo ($user['status'] ?? 'active') === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                            <?php echo ($user['status'] ?? 'active') === 'active' ? 'Активен' : 'Заблокирован'; ?>
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Дата регистрации</label>
                        <p class="text-lg font-semibold text-gray-900"><?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Всего инвестиций</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $investments['total_investments'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Сумма инвестиций</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($investments['total_invested'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Транзакций</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $transactions['total_transactions'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Рефералов</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $referrals['total_referrals'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Последняя активность</h2>
            </div>
            <div class="p-6">
                <?php if (empty($recent_activity)): ?>
                    <p class="text-gray-600 text-center py-8">Нет записей активности</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recent_activity as $activity): ?>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <?php
                                    $icon = 'fas fa-circle';
                                    switch ($activity['type']) {
                                        case 'investment':
                                            $icon = 'fas fa-chart-line';
                                            break;
                                        case 'transaction':
                                            $icon = 'fas fa-exchange-alt';
                                            break;
                                        case 'balance_change':
                                            $icon = 'fas fa-wallet';
                                            break;
                                    }
                                    ?>
                                    <i class="<?php echo $icon; ?> text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900"><?php echo htmlspecialchars($activity['description']); ?></p>
                                    <p class="text-sm text-gray-600"><?php echo date('d.m.Y H:i', strtotime($activity['created_at'])); ?></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900"><?php echo formatCurrency($activity['amount']); ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Быстрые действия</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="openBalanceModal()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-wallet mr-2"></i>Изменить баланс
                    </button>
                    
                    <button onclick="openStatusModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-user-cog mr-2"></i>Изменить статус
                    </button>
                    
                    <button onclick="viewTransactions()" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-list mr-2"></i>Все транзакции
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Balance Modal -->
<div id="balanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Изменить баланс</h3>
            </div>
            
            <form method="POST" action="users.php">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update_balance">
                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Операция</label>
                        <select name="operation" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="add">Добавить</option>
                            <option value="subtract">Вычесть</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Сумма (USDT)</label>
                        <input type="number" name="amount" step="0.01" min="0" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Примечание</label>
                        <textarea name="notes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeBalanceModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        Отмена
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Сохранить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openBalanceModal() {
    document.getElementById('balanceModal').classList.remove('hidden');
}

function closeBalanceModal() {
    document.getElementById('balanceModal').classList.add('hidden');
}

function openStatusModal() {
    // Redirect to users page with user ID for status change
    window.location.href = 'users.php#user-<?php echo $user['id']; ?>';
}

function viewTransactions() {
    window.location.href = 'transactions.php?user_id=<?php echo $user['id']; ?>';
}

// Close modal when clicking outside
document.getElementById('balanceModal').addEventListener('click', function(e) {
    if (e.target === this) closeBalanceModal();
});
</script>

<?php include '../includes/admin_footer.php'; ?>
