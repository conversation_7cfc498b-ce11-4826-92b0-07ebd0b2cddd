<?php
// AstroGenix Referral System Functions
// Complete referral management system for eco mining platform

require_once 'database.php';

/**
 * Generate unique referral code for user
 */
function generateReferralCode($username) {
    $prefix = strtoupper(substr($username, 0, 3));
    $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
    return $prefix . $random;
}

/**
 * Create referral relationship
 */
function createReferral($referrer_id, $referred_id, $referral_code) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO referrals (referrer_id, referred_id, referral_code, commission_rate, status) 
            VALUES (?, ?, ?, 10.00, 'active')
        ");
        return $stmt->execute([$referrer_id, $referred_id, $referral_code]);
    } catch (PDOException $e) {
        error_log("Referral creation error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user by referral code
 */
function getUserByReferralCode($referral_code) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE referral_code = ?");
        $stmt->execute([$referral_code]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get user by referral code error: " . $e->getMessage());
        return false;
    }
}

/**
 * Process referral commission
 */
function processReferralCommission($investment_id, $investment_amount) {
    global $pdo;
    
    try {
        // Get investment details
        $stmt = $pdo->prepare("
            SELECT umi.*, u.referred_by 
            FROM user_mining_investments umi 
            JOIN users u ON umi.user_id = u.id 
            WHERE umi.id = ?
        ");
        $stmt->execute([$investment_id]);
        $investment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$investment || !$investment['referred_by']) {
            return false;
        }
        
        // Get referral relationship
        $stmt = $pdo->prepare("
            SELECT * FROM referrals 
            WHERE referrer_id = ? AND referred_id = ? AND status = 'active'
        ");
        $stmt->execute([$investment['referred_by'], $investment['user_id']]);
        $referral = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$referral) {
            return false;
        }
        
        // Calculate commission
        $commission_amount = ($investment_amount * $referral['commission_rate']) / 100;
        
        // Add commission to referrer's balance
        $stmt = $pdo->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$commission_amount, $referral['referrer_id']]);
        
        // Record referral earning
        $stmt = $pdo->prepare("
            INSERT INTO referral_earnings (referral_id, earning_amount, source_investment_id, earning_type, earning_date) 
            VALUES (?, ?, ?, 'investment_commission', CURDATE())
        ");
        $stmt->execute([$referral['id'], $commission_amount, $investment_id]);
        
        // Update total earned in referrals table
        $stmt = $pdo->prepare("UPDATE referrals SET total_earned = total_earned + ? WHERE id = ?");
        $stmt->execute([$commission_amount, $referral['id']]);
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Referral commission error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user's referral statistics
 */
function getUserReferralStats($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(r.id) as total_referrals,
                COALESCE(SUM(r.total_earned), 0) as total_earnings,
                COUNT(CASE WHEN r.status = 'active' THEN 1 END) as active_referrals,
                u.referral_code
            FROM users u
            LEFT JOIN referrals r ON u.id = r.referrer_id
            WHERE u.id = ?
            GROUP BY u.id, u.referral_code
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get referral stats error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get referral rankings
 */
function getReferralRankings($limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                rr.*,
                u.username,
                u.referral_code
            FROM referral_rankings rr
            JOIN users u ON rr.user_id = u.id
            WHERE rr.total_referrals > 0
            ORDER BY rr.rank_position ASC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get referral rankings error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get mining rankings
 */
function getMiningRankings($limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                mr.*,
                u.username
            FROM mining_rankings mr
            JOIN users u ON mr.user_id = u.id
            WHERE mr.total_mined > 0
            ORDER BY mr.rank_position ASC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get mining rankings error: " . $e->getMessage());
        return [];
    }
}

/**
 * Update user rankings
 */
function updateUserRankings() {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Update mining rankings
        $stmt = $pdo->prepare("
            INSERT INTO mining_rankings (user_id, total_mined, total_co2_saved, total_energy_used, active_investments_count, rank_position)
            SELECT 
                u.id,
                u.total_mined,
                u.total_co2_saved,
                COALESCE(SUM(dmp.energy_used), 0) as total_energy_used,
                COUNT(CASE WHEN umi.is_active = 1 THEN 1 END) as active_investments_count,
                ROW_NUMBER() OVER (ORDER BY u.total_mined DESC) as rank_position
            FROM users u
            LEFT JOIN user_mining_investments umi ON u.id = umi.user_id
            LEFT JOIN daily_mining_profits dmp ON umi.id = dmp.user_investment_id
            WHERE u.role = 'user'
            GROUP BY u.id
            ON DUPLICATE KEY UPDATE
                total_mined = VALUES(total_mined),
                total_co2_saved = VALUES(total_co2_saved),
                total_energy_used = VALUES(total_energy_used),
                active_investments_count = VALUES(active_investments_count),
                rank_position = VALUES(rank_position),
                last_updated = CURRENT_TIMESTAMP
        ");
        $stmt->execute();
        
        // Update referral rankings
        $stmt = $pdo->prepare("
            INSERT INTO referral_rankings (user_id, total_referrals, total_referral_earnings, active_referrals, rank_position)
            SELECT 
                u.id,
                COUNT(r.id) as total_referrals,
                COALESCE(SUM(r.total_earned), 0) as total_referral_earnings,
                COUNT(CASE WHEN r.status = 'active' THEN 1 END) as active_referrals,
                ROW_NUMBER() OVER (ORDER BY COUNT(r.id) DESC, COALESCE(SUM(r.total_earned), 0) DESC) as rank_position
            FROM users u
            LEFT JOIN referrals r ON u.id = r.referrer_id
            WHERE u.role = 'user'
            GROUP BY u.id
            ON DUPLICATE KEY UPDATE
                total_referrals = VALUES(total_referrals),
                total_referral_earnings = VALUES(total_referral_earnings),
                active_referrals = VALUES(active_referrals),
                rank_position = VALUES(rank_position),
                last_updated = CURRENT_TIMESTAMP
        ");
        $stmt->execute();
        
        $pdo->commit();
        return true;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Update rankings error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get referral link for user
 */
function getReferralLink($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT referral_code FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            return SITE_URL . '/pages/register.php?ref=' . $user['referral_code'];
        }
        
        return false;
    } catch (PDOException $e) {
        error_log("Get referral link error: " . $e->getMessage());
        return false;
    }
}

/**
 * Process signup bonus for new referral
 */
function processSignupBonus($referrer_id, $referred_id) {
    global $pdo;
    
    try {
        $signup_bonus = getSiteSetting('signup_bonus', 25);
        
        // Add bonus to referrer's balance
        $stmt = $pdo->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$signup_bonus, $referrer_id]);
        
        // Get referral relationship
        $stmt = $pdo->prepare("SELECT id FROM referrals WHERE referrer_id = ? AND referred_id = ?");
        $stmt->execute([$referrer_id, $referred_id]);
        $referral = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($referral) {
            // Record signup bonus
            $stmt = $pdo->prepare("
                INSERT INTO referral_earnings (referral_id, earning_amount, source_investment_id, earning_type, earning_date) 
                VALUES (?, ?, 0, 'signup_bonus', CURDATE())
            ");
            $stmt->execute([$referral['id'], $signup_bonus]);
            
            // Update total earned
            $stmt = $pdo->prepare("UPDATE referrals SET total_earned = total_earned + ? WHERE id = ?");
            $stmt->execute([$signup_bonus, $referral['id']]);
        }
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Signup bonus error: " . $e->getMessage());
        return false;
    }
}
?>
