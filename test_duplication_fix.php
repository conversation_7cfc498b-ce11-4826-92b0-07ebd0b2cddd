<?php
/**
 * Тест исправления дублирования функций AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест исправления дублирования функций</h1>";

// Тест 1: Подключение основных файлов
echo "<h2>1. Тест подключения файлов</h2>";

try {
    require_once 'config/config.php';
    echo "<p>✅ config/config.php подключен</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка config/config.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'config/database.php';
    echo "<p>✅ config/database.php подключен</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка config/database.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'includes/functions.php';
    echo "<p>✅ includes/functions.php подключен (включает все остальные)</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка includes/functions.php: " . $e->getMessage() . "</p>";
    exit;
}

// Тест 2: Проверка существования ключевых функций
echo "<h2>2. Проверка функций рейтингов</h2>";

$ranking_functions = [
    'getMiningRankings' => 'Рейтинг майнеров',
    'getReferralRankings' => 'Рейтинг рефереров'
];

foreach ($ranking_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ <strong>$function()</strong> - $description доступна</p>";
    } else {
        echo "<p>❌ <strong>$function()</strong> - $description НЕ НАЙДЕНА</p>";
    }
}

// Тест 3: Проверка реферальных функций
echo "<h2>3. Проверка реферальных функций</h2>";

$referral_functions = [
    'getReferralStats' => 'Статистика рефералов',
    'getReferralEarnings' => 'Доходы рефералов',
    'getMyReferrals' => 'Мои рефералы',
    'updateUserReferralCode' => 'Обновление реферального кода',
    'generateReferralCode' => 'Генерация реферального кода'
];

foreach ($referral_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ <strong>$function()</strong> - $description доступна</p>";
    } else {
        echo "<p>❌ <strong>$function()</strong> - $description НЕ НАЙДЕНА</p>";
    }
}

// Тест 4: Проверка подключения к БД
echo "<h2>4. Тест подключения к БД</h2>";

try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
    
    // Проверяем текущую БД
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "<p>📊 Текущая БД: <strong>" . $result['db_name'] . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
}

// Тест 5: Тест функций рейтингов (если БД доступна)
if (isset($db)) {
    echo "<h2>5. Тест функций рейтингов с данными</h2>";
    
    try {
        // Тест getMiningRankings
        if (function_exists('getMiningRankings')) {
            $mining_rankings = getMiningRankings(5);
            echo "<p>✅ getMiningRankings(): найдено " . count($mining_rankings) . " майнеров</p>";
            
            if (!empty($mining_rankings)) {
                echo "<ul>";
                foreach (array_slice($mining_rankings, 0, 3) as $i => $miner) {
                    echo "<li>" . ($i + 1) . ". " . htmlspecialchars($miner['username']) . " - " . formatCurrency($miner['total_mined'] ?? 0) . "</li>";
                }
                echo "</ul>";
            }
        }
        
        // Тест getReferralRankings
        if (function_exists('getReferralRankings')) {
            $referral_rankings = getReferralRankings(5);
            echo "<p>✅ getReferralRankings(): найдено " . count($referral_rankings) . " рефереров</p>";
            
            if (!empty($referral_rankings)) {
                echo "<ul>";
                foreach (array_slice($referral_rankings, 0, 3) as $i => $referrer) {
                    echo "<li>" . ($i + 1) . ". " . htmlspecialchars($referrer['username']) . " - " . ($referrer['total_referrals'] ?? 0) . " рефералов</li>";
                }
                echo "</ul>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка тестирования функций рейтингов: " . $e->getMessage() . "</p>";
    }
}

// Тест 6: Тест реферальных функций (если БД доступна)
if (isset($db)) {
    echo "<h2>6. Тест реферальных функций с данными</h2>";
    
    try {
        // Проверяем, есть ли пользователи
        $stmt = $db->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch()['count'];
        
        if ($user_count > 0) {
            // Берем первого пользователя
            $stmt = $db->query("SELECT id FROM users LIMIT 1");
            $user = $stmt->fetch();
            $user_id = $user['id'];
            
            // Тест getReferralStats
            if (function_exists('getReferralStats')) {
                $stats = getReferralStats($user_id);
                echo "<p>✅ getReferralStats($user_id): работает</p>";
                echo "<ul>";
                echo "<li>Всего рефералов: " . ($stats['total_referrals'] ?? 0) . "</li>";
                echo "<li>Активных рефералов: " . ($stats['active_referrals'] ?? 0) . "</li>";
                echo "<li>Всего заработано: " . formatCurrency($stats['total_earnings'] ?? 0) . "</li>";
                echo "</ul>";
            }
            
            // Тест getMyReferrals
            if (function_exists('getMyReferrals')) {
                $referrals = getMyReferrals($user_id);
                echo "<p>✅ getMyReferrals($user_id): найдено " . count($referrals) . " рефералов</p>";
            }
            
            // Тест getReferralEarnings
            if (function_exists('getReferralEarnings')) {
                $earnings = getReferralEarnings($user_id, 5);
                echo "<p>✅ getReferralEarnings($user_id): найдено " . count($earnings) . " доходов</p>";
            }
            
        } else {
            echo "<p>⚠️ Нет пользователей для тестирования реферальных функций</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка тестирования реферальных функций: " . $e->getMessage() . "</p>";
    }
}

// Тест 7: Проверка страниц
echo "<h2>7. Проверка страниц рейтингов и рефералов</h2>";

$pages_to_check = [
    'pages/rankings.php' => 'Страница рейтингов',
    'pages/referral.php' => 'Реферальная программа'
];

foreach ($pages_to_check as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>❌ $description ($page) не найден</p>";
    }
}

// Итоговый результат
echo "<h2>🎯 Результат тестирования</h2>";

$all_functions_exist = true;
$all_functions = array_merge($ranking_functions, $referral_functions);

foreach ($all_functions as $function => $description) {
    if (!function_exists($function)) {
        $all_functions_exist = false;
        break;
    }
}

if ($all_functions_exist && isset($db)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Дублирование функций успешно устранено!</h3>";
    echo "<p><strong>✅ Исправления:</strong></p>";
    echo "<ul>";
    echo "<li>✅ getMiningRankings() - оставлена только в referral_functions.php</li>";
    echo "<li>✅ getReferralRankings() - оставлена только в referral_functions.php</li>";
    echo "<li>✅ Реферальные функции - остались в functions.php</li>";
    echo "<li>✅ Все функции доступны через автоматическое подключение</li>";
    echo "</ul>";
    echo "<p><strong>🚀 Доступные страницы:</strong></p>";
    echo "<ul>";
    echo "<li><a href='pages/rankings.php' target='_blank'>🏆 Рейтинги платформы</a></li>";
    echo "<li><a href='pages/referral.php' target='_blank'>🤝 Реферальная программа</a></li>";
    echo "<li><a href='pages/dashboard.php' target='_blank'>📊 Дашборд</a></li>";
    echo "<li><a href='admin/index.php' target='_blank'>🔧 Админ-панель</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p>Некоторые функции недоступны или есть проблемы с БД.</p>";
    echo "<p>Рекомендации:</p>";
    echo "<ul>";
    echo "<li>Импортируйте схему БД из database/schema.sql</li>";
    echo "<li>Проверьте настройки подключения к БД</li>";
    echo "<li>Убедитесь, что все файлы на месте</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . " | Тест дублирования функций</small></p>";
?>
