<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Проверка настроек сайта
echo "<h1>🔧 Тест настроек сайта</h1>";

try {
    $db = getDB();
    echo "<p>✅ Подключение к базе данных успешно</p>";
    
    // Проверка существования таблицы
    $stmt = $db->prepare("SHOW TABLES LIKE 'site_settings'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<p>✅ Таблица site_settings существует</p>";
    } else {
        echo "<p>❌ Таблица site_settings не найдена</p>";
        exit;
    }
    
    // Проверка количества настроек
    $stmt = $db->query("SELECT COUNT(*) as count FROM site_settings");
    $count = $stmt->fetch()['count'];
    echo "<p>📊 Количество настроек в базе: $count</p>";
    
    // Получение всех настроек
    $settings = getAllSiteSettings();
    echo "<p>📋 Настройки загружены через функцию: " . count($settings) . " элементов</p>";
    
    echo "<h2>🔍 Все настройки:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Ключ</th><th>Значение</th></tr>";
    
    foreach ($settings as $key => $value) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($key) . "</td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Проверка конкретных настроек
    $required_settings = [
        'site_name', 'site_description', 'contact_email', 
        'contact_phone', 'company_address', 'usdt_wallet_address'
    ];
    
    echo "<h2>🎯 Проверка обязательных настроек:</h2>";
    foreach ($required_settings as $setting) {
        $value = getSiteSetting($setting, 'НЕ НАЙДЕНО');
        $status = ($value !== 'НЕ НАЙДЕНО') ? '✅' : '❌';
        echo "<p>$status $setting: " . htmlspecialchars($value) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка: " . $e->getMessage() . "</p>";
}
?>
