<?php
/**
 * Комплексный тест всех улучшений AstroGenix v2.0
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 Комплексный тест улучшений AstroGenix v2.0</h1>";

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>🎯 1. Тест полного ребрендинга (Poseidon → AstroGenix)</h2>";

// Проверяем файлы на упоминания Poseidon
$files_to_check = [
    'test_function_fix.php',
    'test_redirect.php', 
    'fix_deposit_upload.php',
    'test_enhancements.php'
];

$poseidon_found = false;
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'Poseidon') !== false) {
            echo "<p>⚠️ Найдено упоминание 'Poseidon' в файле: $file</p>";
            $poseidon_found = true;
        } else {
            echo "<p>✅ Файл $file очищен от упоминаний 'Poseidon'</p>";
        }
    }
}

if (!$poseidon_found) {
    echo "<p style='color: green; font-weight: bold;'>✅ Ребрендинг завершен - все упоминания 'Poseidon' заменены на 'AstroGenix'</p>";
}

echo "<h2>📊 2. Тест дашборда с вкладками рейтингов</h2>";

// Проверяем существование обновленного дашборда
if (file_exists('pages/dashboard.php')) {
    $dashboard_content = file_get_contents('pages/dashboard.php');
    
    $features_to_check = [
        'dashboard-tab' => 'Система вкладок',
        'rankings-content' => 'Контент рейтингов',
        'showTab(' => 'JavaScript функции вкладок',
        'getMiningRankings' => 'Функция рейтинга майнеров',
        'getReferralRankings' => 'Функция рейтинга рефереров'
    ];
    
    foreach ($features_to_check as $feature => $description) {
        if (strpos($dashboard_content, $feature) !== false) {
            echo "<p>✅ $description найден в дашборде</p>";
        } else {
            echo "<p>❌ $description НЕ найден в дашборде</p>";
        }
    }
} else {
    echo "<p>❌ Файл pages/dashboard.php не найден</p>";
}

echo "<h2>🎲 3. Тест генератора фейковых данных</h2>";

// Проверяем существование генератора
if (file_exists('admin/fake_data_generator.php')) {
    echo "<p>✅ Генератор фейковых данных создан</p>";
    
    // Проверяем функции генерации
    $generation_functions = [
        'generateFakeUsers' => 'Генерация пользователей',
        'generateFakeTransactions' => 'Генерация транзакций', 
        'generateFakeMiningActivity' => 'Генерация майнинг-активности',
        'clearFakeData' => 'Очистка тестовых данных'
    ];
    
    foreach ($generation_functions as $function => $description) {
        if (function_exists($function)) {
            echo "<p>✅ $description ($function) доступна</p>";
        } else {
            echo "<p>❌ $description ($function) НЕ найдена</p>";
        }
    }
} else {
    echo "<p>❌ Генератор фейковых данных не найден</p>";
}

echo "<h2>💰 4. Тест автоматизации транзакций</h2>";

// Проверяем функции обработки транзакций
$transaction_functions = [
    'processTransaction' => 'Обработка транзакций',
    'logTransaction' => 'Логирование транзакций',
    'getTransactionStats' => 'Статистика транзакций'
];

foreach ($transaction_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ $description ($function) доступна</p>";
    } else {
        echo "<p>❌ $description ($function) НЕ найдена</p>";
    }
}

// Тестируем статистику транзакций
try {
    $stats = getTransactionStats();
    echo "<p>✅ Статистика транзакций работает:</p>";
    echo "<ul>";
    echo "<li>Всего транзакций: " . ($stats['total_transactions'] ?? 0) . "</li>";
    echo "<li>В ожидании: " . ($stats['pending_transactions'] ?? 0) . "</li>";
    echo "<li>Одобрено: " . ($stats['approved_transactions'] ?? 0) . "</li>";
    echo "<li>Отклонено: " . ($stats['rejected_transactions'] ?? 0) . "</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка получения статистики транзакций: " . $e->getMessage() . "</p>";
}

echo "<h2>⚙️ 5. Тест системы управления настройками</h2>";

// Проверяем функции настроек
$settings_functions = [
    'updatePlatformSetting' => 'Обновление настроек',
    'getPlatformSetting' => 'Получение настройки',
    'getAllPlatformSettings' => 'Получение всех настроек',
    'createPlatformSettingsTable' => 'Создание таблицы настроек'
];

foreach ($settings_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ $description ($function) доступна</p>";
    } else {
        echo "<p>❌ $description ($function) НЕ найдена</p>";
    }
}

// Создаем таблицу настроек если её нет
try {
    createPlatformSettingsTable();
    echo "<p>✅ Таблица настроек создана/проверена</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка создания таблицы настроек: " . $e->getMessage() . "</p>";
}

// Тестируем работу с настройками
try {
    $testSetting = 'test_setting_' . time();
    $testValue = 'test_value_' . rand(1000, 9999);
    
    // Сохраняем настройку
    updatePlatformSetting($testSetting, $testValue);
    
    // Получаем настройку
    $retrievedValue = getPlatformSetting($testSetting);
    
    if ($retrievedValue === $testValue) {
        echo "<p>✅ Сохранение и получение настроек работает корректно</p>";
    } else {
        echo "<p>❌ Ошибка сохранения/получения настроек</p>";
    }
    
    // Получаем все настройки
    $allSettings = getAllPlatformSettings();
    echo "<p>✅ Всего настроек в системе: " . count($allSettings) . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка тестирования настроек: " . $e->getMessage() . "</p>";
}

echo "<h2>🔧 6. Проверка админ-инструментов</h2>";

$admin_pages = [
    'admin/fake_data_generator.php' => 'Генератор тестовых данных',
    'admin/settings.php' => 'Настройки платформы',
    'admin/index.php' => 'Главная админ-панели'
];

foreach ($admin_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>❌ $description ($page) не найден</p>";
    }
}

echo "<h2>👥 7. Проверка пользовательских страниц</h2>";

$user_pages = [
    'pages/dashboard.php' => 'Дашборд с рейтингами',
    'pages/rankings.php' => 'Страница рейтингов',
    'pages/referral.php' => 'Реферальная программа'
];

foreach ($user_pages as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>❌ $description ($page) не найден</p>";
    }
}

// Итоговый результат
echo "<h2>🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ УЛУЧШЕНИЙ</h2>";

$all_improvements_ready = true;
$issues = [];

// Проверяем ключевые компоненты
if ($poseidon_found) {
    $all_improvements_ready = false;
    $issues[] = "Не все упоминания 'Poseidon' заменены";
}

if (!function_exists('generateFakeUsers')) {
    $all_improvements_ready = false;
    $issues[] = "Генератор фейковых данных не работает";
}

if (!function_exists('processTransaction')) {
    $all_improvements_ready = false;
    $issues[] = "Автоматизация транзакций не работает";
}

if (!function_exists('updatePlatformSetting')) {
    $all_improvements_ready = false;
    $issues[] = "Система настроек не работает";
}

if ($all_improvements_ready && empty($issues)) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 2px solid #28a745; color: #155724; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);'>";
    echo "<h3 style='margin: 0 0 15px 0; font-size: 24px;'>🎉 ВСЕ УЛУЧШЕНИЯ УСПЕШНО РЕАЛИЗОВАНЫ!</h3>";
    echo "<p><strong>✅ Реализованные функции AstroGenix v2.0:</strong></p>";
    echo "<ul style='margin: 10px 0;'>";
    echo "<li>🎯 <strong>Полный ребрендинг</strong> с Poseidon на AstroGenix</li>";
    echo "<li>📊 <strong>Дашборд с вкладками рейтингов</strong> майнеров и рефереров</li>";
    echo "<li>🎲 <strong>Генератор фейковых данных</strong> для тестирования платформы</li>";
    echo "<li>💰 <strong>Автоматизация обработки транзакций</strong> с балансами</li>";
    echo "<li>⚙️ <strong>Система управления настройками</strong> платформы</li>";
    echo "</ul>";
    echo "<p><strong>🚀 Доступные инструменты и страницы:</strong></p>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 15px 0;'>";
    echo "<a href='pages/dashboard.php' target='_blank' style='background: #28a745; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>📊 Дашборд с рейтингами</a>";
    echo "<a href='admin/fake_data_generator.php' target='_blank' style='background: #6f42c1; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>🎲 Генератор данных</a>";
    echo "<a href='admin/settings.php' target='_blank' style='background: #fd7e14; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>⚙️ Настройки платформы</a>";
    echo "<a href='pages/rankings.php' target='_blank' style='background: #20c997; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>🏆 Рейтинги</a>";
    echo "<a href='pages/referral.php' target='_blank' style='background: #0dcaf0; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>🤝 Реферальная программа</a>";
    echo "</div>";
    echo "<p style='margin: 15px 0 0 0; font-weight: bold; color: #0f5132;'>🌱 Платформа готова к полноценному использованию с экологической майнинг-тематикой!</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p><strong>Найденные проблемы:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "<p><strong>Рекомендации:</strong></p>";
    echo "<ul>";
    echo "<li>Проверьте подключение всех файлов</li>";
    echo "<li>Импортируйте схему БД</li>";
    echo "<li>Убедитесь в правах доступа</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr style='margin: 30px 0; border: 2px solid #28a745;'>";
echo "<div style='text-align: center; background: #f8f9fa; padding: 15px; border-radius: 10px;'>";
echo "<p style='margin: 0; font-weight: bold; color: #495057;'>🚀 AstroGenix Eco Mining Platform v2.0</p>";
echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>Время тестирования: " . date('Y-m-d H:i:s') . " | Все улучшения протестированы</p>";
echo "</div>";
?>
