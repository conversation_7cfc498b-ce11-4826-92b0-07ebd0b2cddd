<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

if ($action === 'create_test_referrals') {
    $count = intval($input['count'] ?? 5);
    $admin_id = getCurrentUserId();
    
    $result = generateTestReferrals($admin_id, $count);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => "Successfully created $count test referrals"
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Failed to create test referrals'
        ]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
}
?>
