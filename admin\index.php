<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$page_title = 'Admin Dashboard';

// Get statistics
$db = getDB();

// Total users
$stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE is_admin = 0");
$stmt->execute();
$total_users = $stmt->fetch()['count'];

// Total mining packages
$stmt = $db->prepare("SELECT COUNT(*) as count FROM mining_packages WHERE is_active = 1");
$stmt->execute();
$total_investments = $stmt->fetch()['count'];

// Total invested amount
$stmt = $db->prepare("SELECT SUM(amount) as total FROM user_mining_investments WHERE is_active = 1");
$stmt->execute();
$total_invested = $stmt->fetch()['total'] ?? 0;

// Pending transactions
$stmt = $db->prepare("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'");
$stmt->execute();
$pending_transactions = $stmt->fetch()['count'];

// Recent activities
$stmt = $db->prepare("
    SELECT 'mining_investment' as type, umi.created_at, u.username, mp.name as title, umi.amount as amount
    FROM user_mining_investments umi
    JOIN users u ON umi.user_id = u.id
    JOIN mining_packages mp ON umi.package_id = mp.id
    ORDER BY umi.created_at DESC
    LIMIT 10
");
$stmt->execute();
$recent_investments = $stmt->fetchAll();

$stmt = $db->prepare("
    SELECT 'transaction' as type, t.created_at, u.username, t.type, t.amount, t.status
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    ORDER BY t.created_at DESC
    LIMIT 10
");
$stmt->execute();
$recent_transactions = $stmt->fetchAll();

// Merge and sort recent activities
$recent_activities = array_merge($recent_investments, $recent_transactions);
usort($recent_activities, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});
$recent_activities = array_slice($recent_activities, 0, 10);

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Панель Администратора</h1>
            <p class="text-gray-600 mt-2">Управление экологической майнинг платформой</p>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <a href="users.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Управление</p>
                        <p class="font-semibold text-gray-900">Пользователи</p>
                    </div>
                </div>
            </a>

            <a href="verifications.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-shield-alt text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Управление</p>
                        <p class="font-semibold text-gray-900">Верификации</p>
                    </div>
                </div>
            </a>

            <a href="mining_packages.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-solar-panel text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Управление</p>
                        <p class="font-semibold text-gray-900">Майнинг-фермы</p>
                    </div>
                </div>
            </a>

            <a href="transactions.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-exchange-alt text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Обработка</p>
                        <p class="font-semibold text-gray-900">Транзакции</p>
                    </div>
                </div>
            </a>

            <a href="blog.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-newspaper text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Управление</p>
                        <p class="font-semibold text-gray-900">Блог</p>
                    </div>
                </div>
            </a>

            <a href="settings.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-xl transition-all duration-300 border border-gray-100 group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-700 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-cog text-white text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-gray-600">Настройки</p>
                        <p class="font-semibold text-gray-900">Сайта</p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего пользователей</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($total_users); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Активные инвестиции</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($total_investments); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Общая сумма инвестиций</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($total_invested); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Ожидающие транзакции</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($pending_transactions); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Activities -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Недавние активности</h2>
                </div>
                <div class="p-6">
                    <?php if (empty($recent_activities)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-600">Нет недавних активностей</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <?php if ($activity['type'] === 'mining_investment'): ?>
                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-solar-panel text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo htmlspecialchars($activity['username']); ?></p>
                                                <p class="text-sm text-gray-600">Started mining with <?php echo htmlspecialchars($activity['title']); ?></p>
                                                <p class="text-xs text-gray-500"><?php echo timeAgo($activity['created_at']); ?></p>
                                            </div>
                                        <?php else: ?>
                                            <div class="w-10 h-10 <?php echo $activity['type'] === 'deposit' ? 'bg-blue-100' : 'bg-red-100'; ?> rounded-full flex items-center justify-center">
                                                <i class="fas <?php echo $activity['type'] === 'deposit' ? 'fa-arrow-down text-blue-600' : 'fa-arrow-up text-red-600'; ?>"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo htmlspecialchars($activity['username']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo ucfirst($activity['type']); ?> request</p>
                                                <p class="text-xs text-gray-500"><?php echo timeAgo($activity['created_at']); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold"><?php echo formatCurrency($activity['amount']); ?></p>
                                        <?php if (isset($activity['status'])): ?>
                                            <span class="text-xs px-2 py-1 rounded-full <?php 
                                                echo $activity['status'] === 'approved' ? 'bg-green-100 text-green-800' : 
                                                    ($activity['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                                            ?>">
                                                <?php echo ucfirst($activity['status']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="space-y-6">
                <!-- Pending Transactions Alert -->
                <?php if ($pending_transactions > 0): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 shadow-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl mr-4"></i>
                        <div>
                            <h3 class="text-lg font-bold text-yellow-800">Требуется внимание</h3>
                            <p class="text-yellow-700">У вас есть <?php echo $pending_transactions; ?> ожидающих транзакций для проверки.</p>
                            <div class="mt-3 space-x-3">
                                <a href="transactions.php" class="text-yellow-800 hover:text-yellow-900 font-semibold transition-colors">Проверить транзакции →</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- System Status -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Статус системы</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">База данных</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                Онлайн
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Ежедневный расчет прибыли</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                Активен
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Последний запуск</span>
                            <span class="text-gray-800"><?php echo date('j M Y'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Быстрые действия</h3>
                    <div class="space-y-3">
                        <button onclick="runDailyProfit()" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-play mr-2"></i>Запустить расчет прибыли
                        </button>
                        <a href="mining_packages.php" class="block w-full text-center border-2 border-blue-500 text-blue-600 hover:bg-blue-50 py-2 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-plus mr-2"></i>Добавить майнинг-пакет
                        </a>
                        <a href="../cron/daily_profit.php" target="_blank" class="block w-full text-center border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 py-2 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-eye mr-2"></i>Просмотр логов
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function runDailyProfit() {
    if (confirm('Вы уверены, что хотите запустить ежедневный расчет прибыли вручную?')) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Выполняется...';
        btn.disabled = true;

        // Make AJAX request to run the cron job
        fetch('../cron/daily_profit.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.text())
        .then(data => {
            alert('Расчет ежедневной прибыли завершен. Проверьте логи для получения подробностей.');
            btn.innerHTML = originalText;
            btn.disabled = false;
            location.reload();
        })
        .catch(error => {
            alert('Ошибка при выполнении расчета прибыли: ' + error);
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
