<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check authorization
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = getUserById($user_id);

// Generate referral code if not exists
if (empty($user['referral_code'])) {
    $referral_code = generateReferralCode();
    updateUserReferralCode($user_id, $referral_code);
    $user['referral_code'] = $referral_code;
}

// Get referral statistics
$referral_stats = getReferralStats($user_id);
$referral_earnings = getReferralEarnings($user_id);
$my_referrals = getMyReferrals($user_id);

// Debug information (remove in production)
error_log("Referral stats for user $user_id: " . print_r($referral_stats, true));
error_log("My referrals count: " . count($my_referrals));

$page_title = 'Referral Program - AstroGenix';
$page_description = 'Invite friends and earn from eco mining';

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-green-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-slate-900 via-green-900 to-slate-800 text-white py-20 relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-purple-500/20"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <div class="inline-block px-6 py-3 bg-white bg-opacity-15 rounded-full text-sm font-medium mb-4 backdrop-blur-sm border border-white border-opacity-20 shadow-lg">
                    🤝 Referral Program
                </div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                    Invite <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-purple-400">Friends</span>
                </h1>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                    Earn up to 10% from each investment of your referrals in eco mining
                </p>
            </div>
        </div>
    </section>

    <!-- Referral Dashboard -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Referrals</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $referral_stats['total_referrals'] ?? 0; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-check text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Active Referrals</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $referral_stats['active_referrals'] ?? 0; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-coins text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Earned</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($referral_stats['total_earnings'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-day text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">This Month</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($referral_stats['monthly_earnings'] ?? 0); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Referral Link Section -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Referral Link</h2>
                        <p class="text-gray-600">Share this link with your friends</p>
                    </div>
                    
                    <div class="p-6">
                        <div class="bg-gray-50 rounded-xl p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Referral Code</label>
                                    <input type="text" id="referral-code" value="<?php echo htmlspecialchars($user['referral_code']); ?>" readonly 
                                           class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg font-mono text-lg">
                                </div>
                                <button onclick="copyReferralCode()" class="ml-4 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4 mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Link</label>
                            <div class="flex items-center">
                                <input type="text" id="referral-link" value="<?php echo SITE_URL; ?>/pages/register.php?ref=<?php echo htmlspecialchars($user['referral_code']); ?>" readonly 
                                       class="flex-1 px-4 py-3 bg-white border border-gray-300 rounded-l-lg text-sm">
                                <button onclick="copyReferralLink()" class="px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-r-lg transition-colors">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Commission Structure -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Commission Structure</h2>
                        <p class="text-gray-600">Your earnings from the referral program</p>
                    </div>
                    
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl border border-green-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-white"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-semibold text-gray-900">Registration Bonus</p>
                                        <p class="text-sm text-gray-600">For each new user</p>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-green-600">$5</span>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-percentage text-white"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-semibold text-gray-900">Investment Commission</p>
                                        <p class="text-sm text-gray-600">From each mining investment</p>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-blue-600">10%</span>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-purple-50 rounded-xl border border-purple-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-coins text-white"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-semibold text-gray-900">Profit Commission</p>
                                        <p class="text-sm text-gray-600">From daily profits of referrals</p>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-purple-600">5%</span>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                            <div class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-3"></i>
                                <div>
                                    <p class="font-semibold text-gray-900 mb-1">Tip</p>
                                    <p class="text-sm text-gray-600">The more your referrals invest in eco mining, the more you earn!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Referrals -->
            <?php if (!empty($my_referrals)): ?>
            <div class="mt-12">
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">My Referrals</h2>
                        <p class="text-gray-600">List of users you have invited</p>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Registration Date</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Investments</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Your Income</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($my_referrals as $referral): ?>
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                                                <span class="text-white font-semibold"><?php echo strtoupper(substr($referral['username'], 0, 1)); ?></span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($referral['username']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-gray-600"><?php echo date('d.m.Y', strtotime($referral['registration_date'] ?? $referral['created_at'])); ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo ($referral['is_active_referral'] ?? 0) ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                            <?php echo ($referral['is_active_referral'] ?? 0) ? 'Active' : 'Pending'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-lg font-semibold text-blue-600"><?php echo $referral['total_investments'] ?? 0; ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-lg font-semibold text-green-600"><?php echo formatCurrency($referral['my_earnings'] ?? 0); ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recent Earnings -->
            <?php if (!empty($referral_earnings)): ?>
            <div class="mt-12">
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Recent Earnings</h2>
                        <p class="text-gray-600">History of your referral earnings</p>
                    </div>
                    
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach (array_slice($referral_earnings, 0, 10) as $earning): ?>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-coins text-white"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="font-semibold text-gray-900"><?php echo ucfirst(str_replace('_', ' ', $earning['earning_type'])); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo date('d.m.Y H:i', strtotime($earning['created_at'])); ?></p>
                                    </div>
                                </div>
                                <span class="text-lg font-bold text-green-600">+<?php echo formatCurrency($earning['amount']); ?></span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<script>
function copyReferralCode() {
    const codeInput = document.getElementById('referral-code');
    codeInput.select();
    document.execCommand('copy');
    
    showNotification('Referral code copied!', 'success');
}

function copyReferralLink() {
    const linkInput = document.getElementById('referral-link');
    linkInput.select();
    document.execCommand('copy');
    
    showNotification('Referral link copied!', 'success');
}



function showNotification(message, type) {
    // Create notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<?php include '../includes/footer.php'; ?>
