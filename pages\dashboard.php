<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireLogin();

$page_title = 'Dashboard';
$user_id = getCurrentUserId();

// Get dashboard statistics
$stats = getDashboardStats($user_id);

// Get user investments
$user_investments = getUserInvestments($user_id);

// Get recent transactions
$recent_transactions = getUserTransactions($user_id, 5);

// Dashboard now uses separate pages for deposit and withdrawal
// No POST handling needed here

// Add animation assets to header
$additional_css = ['../assets/css/investment-animation.css'];
$additional_js = ['../assets/js/investment-animation.js'];

include '../includes/header.php';

// Check if user was redirected after successful investment
$show_animation = isset($_GET['investment_success']) && $_GET['investment_success'] == '1';
$investment_amount = $_GET['amount'] ?? 0;
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Welcome back, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h1>
            <p class="text-gray-600 mt-2">Manage your eco mining farms and track your green portfolio performance</p>
        </div>

        <!-- Verification Status Alert -->
        <?php
        $user = getUserById($user_id);
        $verification_status = $user['verification_status'] ?? 'unverified';
        $kyc_required = isKYCRequired(); // Check if KYC is enabled in admin settings

        // Only show verification alerts if KYC is required AND user is not verified
        if ($kyc_required && $verification_status !== 'verified'):
        ?>
        <div class="mb-8">
            <?php if ($verification_status === 'unverified'): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-xl mr-3"></i>
                        <div class="flex-1">
                            <h3 class="text-lg font-medium text-yellow-800">Account Verification Required</h3>
                            <p class="text-yellow-700 mt-1">You need to verify your account to start eco mining. You can still deposit funds, but mining farm access requires verification.</p>
                        </div>
                        <a href="verification.php" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Verify Now
                        </a>
                    </div>
                </div>
            <?php elseif ($verification_status === 'pending'): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-blue-500 text-xl mr-3"></i>
                        <div class="flex-1">
                            <h3 class="text-lg font-medium text-blue-800">Verification Under Review</h3>
                            <p class="text-blue-700 mt-1">Your verification documents are being reviewed. This usually takes 24-48 hours.</p>
                        </div>
                        <a href="verification.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            View Status
                        </a>
                    </div>
                </div>
            <?php elseif ($verification_status === 'rejected'): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-500 text-xl mr-3"></i>
                        <div class="flex-1">
                            <h3 class="text-lg font-medium text-red-800">Verification Rejected</h3>
                            <p class="text-red-700 mt-1">Your verification was rejected. Please submit new documents to continue.</p>
                        </div>
                        <a href="verification.php" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Resubmit
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-wallet text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Available Balance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($stats['balance']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Invested</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($stats['total_invested']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Profit</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo formatCurrency($stats['total_profit']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-briefcase text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Active Investments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['active_investments']; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Tabs -->
        <div class="mb-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="showTab('overview')" id="overview-tab" class="dashboard-tab active py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Overview
                        </button>
                        <button onclick="showTab('rankings')" id="rankings-tab" class="dashboard-tab py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                            <i class="fas fa-trophy mr-2"></i>
                            Rankings
                        </button>
                        <button onclick="showTab('transactions')" id="transactions-tab" class="dashboard-tab py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                            <i class="fas fa-exchange-alt mr-2"></i>
                            Transactions
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Overview Tab -->
                    <div id="overview-content" class="tab-content">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Active Investments -->
                <div class="bg-white rounded-xl card-shadow border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-bold text-gray-900">Active Investments</h2>
                            <a href="investments.php" class="text-blue-600 hover:text-purple-600 font-semibold transition-colors">
                                Browse More →
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($user_investments)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600 mb-4">You haven't made any investments yet</p>
                                <a href="investments.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    Start Investing
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($user_investments as $investment): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 group">
                                        <div class="flex justify-between items-start">
                                            <div class="flex items-center space-x-4">
                                                <img src="<?php echo $investment['image_url'] ?: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>"
                                                     alt="<?php echo htmlspecialchars($investment['title']); ?>"
                                                     class="w-16 h-16 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors"><?php echo htmlspecialchars($investment['title']); ?></h3>
                                                    <p class="text-sm text-gray-600"><?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?></p>
                                                    <p class="text-sm text-gray-500">Started: <?php echo date('M j, Y', strtotime($investment['start_date'])); ?></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-lg font-bold text-gray-900"><?php echo formatCurrency($investment['amount']); ?></p>
                                                <p class="text-sm text-green-600"><?php echo formatPercentage($investment['monthly_rate']); ?> monthly</p>
                                                <p class="text-sm text-gray-500">Profit: <?php echo formatCurrency($investment['total_profit']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl card-shadow border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Recent Transactions</h2>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_transactions)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600">No transactions yet</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center space-x-3">
                                            <?php
                                            $iconClass = 'fa-arrow-down text-green-600';
                                            $bgClass = 'bg-green-100';
                                            $displayType = ucfirst($transaction['type']);

                                            if ($transaction['type'] === 'withdrawal') {
                                                $iconClass = 'fa-arrow-up text-red-600';
                                                $bgClass = 'bg-red-100';
                                            } elseif ($transaction['type'] === 'profit_credit') {
                                                $iconClass = 'fa-coins text-yellow-600';
                                                $bgClass = 'bg-yellow-100';
                                                $displayType = 'Daily Profit';
                                            }
                                            ?>
                                            <div class="w-10 h-10 rounded-full flex items-center justify-center <?php echo $bgClass; ?>">
                                                <i class="fas <?php echo $iconClass; ?>"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo $displayType; ?></p>
                                                <p class="text-sm text-gray-500"><?php echo timeAgo($transaction['created_at']); ?></p>
                                                <?php if ($transaction['type'] === 'profit_credit' && !empty($transaction['admin_notes'])): ?>
                                                    <p class="text-xs text-gray-400"><?php echo htmlspecialchars($transaction['admin_notes']); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <?php
                                            $amountClass = 'text-green-600';
                                            $amountPrefix = '+';

                                            if ($transaction['type'] === 'withdrawal') {
                                                $amountClass = 'text-red-600';
                                                $amountPrefix = '-';
                                            } elseif ($transaction['type'] === 'profit_credit') {
                                                $amountClass = 'text-yellow-600';
                                                $amountPrefix = '+';
                                            }
                                            ?>
                                            <p class="font-semibold <?php echo $amountClass; ?>">
                                                <?php echo $amountPrefix; ?><?php echo formatCurrency($transaction['amount']); ?>
                                            </p>
                                            <span class="text-xs px-2 py-1 rounded-full <?php
                                                if ($transaction['status'] === 'completed') {
                                                    echo 'bg-green-100 text-green-800';
                                                } elseif ($transaction['status'] === 'approved') {
                                                    echo 'bg-green-100 text-green-800';
                                                } elseif ($transaction['status'] === 'rejected') {
                                                    echo 'bg-red-100 text-red-800';
                                                } else {
                                                    echo 'bg-yellow-100 text-yellow-800';
                                                }
                                            ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="deposit.php" class="block w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg text-center">
                            <i class="fas fa-plus mr-2"></i>Deposit Funds
                        </a>
                        <a href="withdrawal.php" class="block w-full border-2 border-blue-500 text-blue-600 hover:bg-blue-50 py-3 rounded-lg font-semibold transition-all duration-300 text-center">
                            <i class="fas fa-minus mr-2"></i>Withdraw Funds
                        </a>
                        <a href="investments.php" class="block w-full text-center border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-search mr-2"></i>Browse Investments
                        </a>
                    </div>
                </div>

                <!-- Account Info -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Account Information</h3>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-600">Username</p>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($_SESSION['username']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Email</p>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($_SESSION['email']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Member Since</p>
                            <p class="font-semibold text-gray-900"><?php echo date('M Y'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Referral Program -->
                <?php
                // Get user referral data
                $user = getUserById($user_id);
                $referral_code = $user['referral_code'] ?? '';
                $referral_link = "https://" . $_SERVER['HTTP_HOST'] . "/pages/register.php?ref=" . $referral_code;

                // Get referral statistics
                $referral_stats = getReferralStats($user_id);
                $my_referrals = getMyReferrals($user_id);
                ?>

                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-gray-900">🤝 Referral Program</h3>
                        <a href="referral.php" class="text-purple-600 hover:text-purple-700 text-sm font-semibold">
                            Learn More →
                        </a>
                    </div>

                    <!-- Referral Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-white rounded-lg p-3 text-center">
                            <div class="text-2xl font-bold text-purple-600"><?php echo $referral_stats['total_referrals'] ?? 0; ?></div>
                            <div class="text-xs text-gray-600">Referrals</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 text-center">
                            <div class="text-2xl font-bold text-green-600"><?php echo formatCurrency($referral_stats['total_earnings'] ?? 0); ?></div>
                            <div class="text-xs text-gray-600">Earned</div>
                        </div>
                    </div>

                    <!-- Referral Code -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Your referral code:</label>
                        <div class="flex">
                            <input type="text" value="<?php echo htmlspecialchars($referral_code); ?>"
                                   id="referral-code" readonly
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50 text-sm">
                            <button onclick="copyReferralCode()"
                                    class="px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-r-lg transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Referral Link -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Referral link:</label>
                        <div class="flex">
                            <input type="text" value="<?php echo htmlspecialchars($referral_link); ?>"
                                   id="referral-link" readonly
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50 text-sm">
                            <button onclick="copyReferralLink()"
                                    class="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-r-lg transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>



                    <!-- Recent Referrals -->
                    <?php if (!empty($my_referrals)): ?>
                    <div class="mt-4">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2">Recent referrals:</h4>
                        <div class="space-y-2">
                            <?php foreach (array_slice($my_referrals, 0, 3) as $referral): ?>
                            <div class="flex items-center justify-between bg-white rounded-lg p-2">
                                <div class="flex items-center">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-purple-600 text-xs"></i>
                                    </div>
                                    <div class="ml-2">
                                        <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($referral['username']); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo date('d.m.Y', strtotime($referral['created_at'])); ?></p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-green-600 font-semibold"><?php echo formatCurrency($referral['my_earnings'] ?? 0); ?></p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Support -->
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white shadow-lg">
                    <h3 class="text-lg font-bold mb-2">Need Help?</h3>
                    <p class="text-sm mb-4 text-blue-100">Our support team is available 24/7 to assist you</p>
                    <a href="contact.php" class="bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold text-sm hover:bg-blue-50 transition-colors">
                        Contact Support
                    </a>
                </div>
            </div>

                    <!-- Rankings Tab -->
                    <div id="rankings-content" class="tab-content hidden">
                        <?php
                        // Get rankings
                        $mining_rankings = getMiningRankings(10);
                        $referral_rankings = getReferralRankings(10);

                        // Find current user position
                        $user_mining_position = null;
                        $user_referral_position = null;

                        foreach ($mining_rankings as $index => $miner) {
                            if ($miner['id'] == $user_id) {
                                $user_mining_position = $index + 1;
                                break;
                            }
                        }

                        foreach ($referral_rankings as $index => $referrer) {
                            if ($referrer['id'] == $user_id) {
                                $user_referral_position = $index + 1;
                                break;
                            }
                        }
                        ?>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Mining Rankings -->
                            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-gray-900">🏆 Miners Ranking</h3>
                                    <a href="rankings.php" class="text-green-600 hover:text-green-700 font-semibold text-sm">
                                        Full Ranking →
                                    </a>
                                </div>

                                <?php if ($user_mining_position): ?>
                                <div class="bg-white rounded-lg p-4 mb-4 border-2 border-green-300">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                            <span class="text-white font-bold"><?php echo $user_mining_position; ?></span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-semibold text-gray-900">Your Position</p>
                                            <p class="text-sm text-gray-600">Place in miners ranking</p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="space-y-3">
                                    <?php foreach (array_slice($mining_rankings, 0, 5) as $index => $miner): ?>
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 <?php echo $miner['id'] == $user_id ? 'ring-2 ring-green-300' : ''; ?>">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-bold text-sm"><?php echo $index + 1; ?></span>
                                            </div>
                                            <div class="ml-3">
                                                <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($miner['username']); ?></p>
                                                <p class="text-xs text-gray-600"><?php echo number_format($miner['total_co2_saved'], 1); ?> kg CO₂</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-green-600"><?php echo formatCurrency($miner['total_mined']); ?></p>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Referral Rankings -->
                            <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-gray-900">👥 Referrers Ranking</h3>
                                    <a href="rankings.php" class="text-purple-600 hover:text-purple-700 font-semibold text-sm">
                                        Full Ranking →
                                    </a>
                                </div>

                                <?php if ($user_referral_position): ?>
                                <div class="bg-white rounded-lg p-4 mb-4 border-2 border-purple-300">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                                            <span class="text-white font-bold"><?php echo $user_referral_position; ?></span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-semibold text-gray-900">Your Position</p>
                                            <p class="text-sm text-gray-600">Place in referrers ranking</p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="space-y-3">
                                    <?php foreach (array_slice($referral_rankings, 0, 5) as $index => $referrer): ?>
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 <?php echo $referrer['id'] == $user_id ? 'ring-2 ring-purple-300' : ''; ?>">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-bold text-sm"><?php echo $index + 1; ?></span>
                                            </div>
                                            <div class="ml-3">
                                                <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($referrer['username']); ?></p>
                                                <p class="text-xs text-gray-600"><?php echo $referrer['total_referrals']; ?> referrals</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-purple-600"><?php echo formatCurrency($referrer['total_referral_earnings']); ?></p>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transactions Tab -->
                    <div id="transactions-content" class="tab-content hidden">
                        <div class="bg-white rounded-xl border border-gray-200">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-xl font-bold text-gray-900">📊 Recent Transactions</h3>
                            </div>
                            <div class="p-6">
                                <?php if (empty($recent_transactions)): ?>
                                    <div class="text-center py-8">
                                        <i class="fas fa-exchange-alt text-gray-400 text-4xl mb-4"></i>
                                        <p class="text-gray-600 mb-4">You have no transactions yet</p>
                                        <a href="deposit.php" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300">
                                            Make Deposit
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="space-y-4">
                                        <?php foreach ($recent_transactions as $transaction): ?>
                                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 rounded-full flex items-center justify-center <?php echo $transaction['type'] === 'deposit' ? 'bg-green-100' : 'bg-red-100'; ?>">
                                                        <i class="fas <?php echo $transaction['type'] === 'deposit' ? 'fa-plus text-green-600' : 'fa-minus text-red-600'; ?>"></i>
                                                    </div>
                                                    <div class="ml-4">
                                                        <p class="font-semibold text-gray-900">
                                                            <?php echo $transaction['type'] === 'deposit' ? 'Deposit' : 'Withdrawal'; ?>
                                                        </p>
                                                        <p class="text-sm text-gray-600">
                                                            <?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <p class="font-bold <?php echo $transaction['type'] === 'deposit' ? 'text-green-600' : 'text-red-600'; ?>">
                                                        <?php echo $transaction['type'] === 'deposit' ? '+' : '-'; ?><?php echo formatCurrency($transaction['amount']); ?>
                                                    </p>
                                                    <p class="text-sm <?php
                                                        echo $transaction['status'] === 'approved' ? 'text-green-600' :
                                                            ($transaction['status'] === 'rejected' ? 'text-red-600' : 'text-yellow-600');
                                                    ?>">
                                                        <?php
                                                        echo $transaction['status'] === 'approved' ? 'Approved' :
                                                            ($transaction['status'] === 'rejected' ? 'Rejected' : 'Processing');
                                                        ?>
                                                    </p>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="mt-6 text-center">
                                        <a href="transactions.php" class="text-blue-600 hover:text-purple-600 font-semibold">
                                            View All Transactions →
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<style>
.dashboard-tab {
    color: #6b7280;
    border-color: transparent;
}

.dashboard-tab.active {
    color: #10b981;
    border-color: #10b981;
}

.dashboard-tab:hover {
    color: #059669;
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none;
}
</style>

<script>
// Tab functions
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    document.querySelectorAll('.dashboard-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab
    document.getElementById(tabName + '-tab').classList.add('active');
}

// Initialize with overview tab
document.addEventListener('DOMContentLoaded', function() {
    showTab('overview');
});

// Alert functions
function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('[id$="-alert"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                closeAlert(alert.id);
            }
        }, 5000);
    });
});

// Referral functions
function copyReferralCode() {
    const codeInput = document.getElementById('referral-code');
    codeInput.select();
    codeInput.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showNotification('Referral code copied!', 'success');
    } catch (err) {
        showNotification('Copy error', 'error');
    }
}

function copyReferralLink() {
    const linkInput = document.getElementById('referral-link');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showNotification('Referral link copied!', 'success');
    } catch (err) {
        showNotification('Copy error', 'error');
    }
}



function showNotification(message, type) {
    // Create notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

<?php if ($show_animation): ?>
// Show investment success animation
document.addEventListener('DOMContentLoaded', function() {
    if (typeof showInvestmentSuccess === 'function') {
        const animationData = {
            amount: '<?php echo number_format($investment_amount, 2); ?>',
            dailyProfit: '<?php echo number_format($investment_amount * 0.02, 2); ?>',
            duration: '365',
            packageName: 'Eco Mining Package'
        };

        setTimeout(() => {
            showInvestmentSuccess(animationData);
        }, 500);
    }
});
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
