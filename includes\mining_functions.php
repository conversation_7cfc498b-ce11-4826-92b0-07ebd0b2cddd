<?php
// AstroGenix Mining Functions
// Complete mining package management system

require_once 'database.php';

/**
 * Get all active mining packages
 */
function getAllMiningPackages($limit = null) {
    global $pdo;
    
    try {
        $sql = "
            SELECT * FROM mining_packages 
            WHERE is_active = 1 
            ORDER BY created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON features for each package
        foreach ($packages as &$package) {
            $package['features'] = json_decode($package['features'], true) ?: [];
        }
        
        return $packages;
    } catch (PDOException $e) {
        error_log("Get mining packages error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get mining package by ID
 */
function getMiningPackageById($package_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM mining_packages WHERE id = ? AND is_active = 1");
        $stmt->execute([$package_id]);
        $package = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($package) {
            $package['features'] = json_decode($package['features'], true) ?: [];
        }
        
        return $package;
    } catch (PDOException $e) {
        error_log("Get mining package error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create user mining investment
 */
function createMiningInvestment($user_id, $package_id, $amount) {
    global $pdo;
    
    try {
        $package = getMiningPackageById($package_id);
        if (!$package) {
            return ['success' => false, 'message' => 'Invalid mining package'];
        }
        
        if ($amount < $package['min_investment'] || ($package['max_investment'] && $amount > $package['max_investment'])) {
            return ['success' => false, 'message' => 'Investment amount out of range'];
        }
        
        $user = getUserById($user_id);
        if ($user['balance'] < $amount) {
            return ['success' => false, 'message' => 'Insufficient balance'];
        }
        
        $pdo->beginTransaction();
        
        // Calculate dates
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+' . $package['duration_days'] . ' days'));
        
        // Calculate allocated hash power based on investment amount
        $hash_power_ratio = $amount / $package['min_investment'];
        $allocated_hash_power = round($hash_power_ratio * floatval(str_replace(' TH/s', '', $package['hash_power'])), 2) . ' TH/s';
        
        // Create investment
        $stmt = $pdo->prepare("
            INSERT INTO user_mining_investments 
            (user_id, package_id, amount, daily_rate, start_date, end_date, hash_power_allocated) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user_id, 
            $package_id, 
            $amount, 
            $package['daily_rate'], 
            $start_date, 
            $end_date,
            $allocated_hash_power
        ]);
        
        $investment_id = $pdo->lastInsertId();
        
        // Deduct amount from user balance
        $stmt = $pdo->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);
        
        // Process referral commission if applicable
        require_once 'referral_functions.php';
        processReferralCommission($investment_id, $amount);
        
        $pdo->commit();
        
        return [
            'success' => true, 
            'message' => 'Mining investment created successfully',
            'investment_id' => $investment_id
        ];
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Create mining investment error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Database error occurred'];
    }
}

/**
 * Get user's mining investments
 */
function getUserMiningInvestments($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                umi.*,
                mp.name as package_name,
                mp.energy_type,
                mp.location,
                mp.image_url
            FROM user_mining_investments umi
            JOIN mining_packages mp ON umi.package_id = mp.id
            WHERE umi.user_id = ?
            ORDER BY umi.created_at DESC
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get user mining investments error: " . $e->getMessage());
        return [];
    }
}

/**
 * Process daily mining profits
 */
function processDailyMiningProfits() {
    global $pdo;
    
    try {
        $today = date('Y-m-d');
        
        // Get all active investments for today
        $stmt = $pdo->prepare("
            SELECT 
                umi.*,
                mp.co2_saved as package_co2_saved,
                mp.energy_consumption as package_energy_consumption
            FROM user_mining_investments umi
            JOIN mining_packages mp ON umi.package_id = mp.id
            WHERE umi.is_active = 1 
            AND umi.start_date <= ? 
            AND umi.end_date >= ?
            AND NOT EXISTS (
                SELECT 1 FROM daily_mining_profits dmp 
                WHERE dmp.user_investment_id = umi.id 
                AND dmp.profit_date = ?
            )
        ");
        $stmt->execute([$today, $today, $today]);
        $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($investments as $investment) {
            // Calculate daily profit
            $daily_profit = $investment['amount'] * ($investment['daily_rate'] / 100);
            
            // Calculate CO2 saved and energy used based on investment ratio
            $investment_ratio = $investment['amount'] / 1000; // Base ratio
            $co2_saved = $investment['package_co2_saved'] * $investment_ratio;
            $energy_used = $investment['package_energy_consumption'] * $investment_ratio;
            
            // Insert daily profit record
            $stmt = $pdo->prepare("
                INSERT INTO daily_mining_profits 
                (user_investment_id, mined_amount, co2_saved, energy_used, profit_date) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $investment['id'], 
                $daily_profit, 
                $co2_saved, 
                $energy_used, 
                $today
            ]);
            
            // Update user balance
            $stmt = $pdo->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
            $stmt->execute([$daily_profit, $investment['user_id']]);
            
            // Update investment totals
            $stmt = $pdo->prepare("
                UPDATE user_mining_investments 
                SET total_mined = total_mined + ?, total_co2_saved = total_co2_saved + ? 
                WHERE id = ?
            ");
            $stmt->execute([$daily_profit, $co2_saved, $investment['id']]);
            
            // Update user totals
            $stmt = $pdo->prepare("
                UPDATE users 
                SET total_mined = total_mined + ?, total_co2_saved = total_co2_saved + ? 
                WHERE id = ?
            ");
            $stmt->execute([$daily_profit, $co2_saved, $investment['user_id']]);
        }
        
        return count($investments);
        
    } catch (PDOException $e) {
        error_log("Process daily mining profits error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get mining statistics for dashboard
 */
function getMiningStatistics($user_id = null) {
    global $pdo;
    
    try {
        if ($user_id) {
            // User-specific statistics
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(umi.id) as total_investments,
                    COUNT(CASE WHEN umi.is_active = 1 THEN 1 END) as active_investments,
                    COALESCE(SUM(umi.amount), 0) as total_invested,
                    COALESCE(SUM(umi.total_mined), 0) as total_mined,
                    COALESCE(SUM(umi.total_co2_saved), 0) as total_co2_saved,
                    u.balance as current_balance
                FROM users u
                LEFT JOIN user_mining_investments umi ON u.id = umi.user_id
                WHERE u.id = ?
                GROUP BY u.id, u.balance
            ");
            $stmt->execute([$user_id]);
        } else {
            // Platform-wide statistics
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(DISTINCT u.id) as total_users,
                    COUNT(umi.id) as total_investments,
                    COUNT(CASE WHEN umi.is_active = 1 THEN 1 END) as active_investments,
                    COALESCE(SUM(umi.amount), 0) as total_invested,
                    COALESCE(SUM(umi.total_mined), 0) as total_mined,
                    COALESCE(SUM(umi.total_co2_saved), 0) as total_co2_saved
                FROM user_mining_investments umi
                JOIN users u ON umi.user_id = u.id
                WHERE u.role = 'user'
            ");
            $stmt->execute();
        }
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get mining statistics error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent mining activity
 */
function getRecentMiningActivity($user_id = null, $limit = 10) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                dmp.*,
                umi.amount as investment_amount,
                mp.name as package_name,
                mp.energy_type,
                u.username
            FROM daily_mining_profits dmp
            JOIN user_mining_investments umi ON dmp.user_investment_id = umi.id
            JOIN mining_packages mp ON umi.package_id = mp.id
            JOIN users u ON umi.user_id = u.id
        ";
        
        $params = [];
        if ($user_id) {
            $sql .= " WHERE umi.user_id = ?";
            $params[] = $user_id;
        }
        
        $sql .= " ORDER BY dmp.profit_date DESC, dmp.created_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get recent mining activity error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get eco news
 */
function getEcoNews($limit = 5, $featured_only = false) {
    global $pdo;
    
    try {
        $sql = "
            SELECT * FROM eco_news 
            WHERE is_published = 1
        ";
        
        if ($featured_only) {
            $sql .= " AND is_featured = 1";
        }
        
        $sql .= " ORDER BY published_at DESC, created_at DESC LIMIT ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get eco news error: " . $e->getMessage());
        return [];
    }
}
?>
