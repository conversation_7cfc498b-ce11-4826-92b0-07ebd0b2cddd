<?php
/**
 * Тест подключения к базе данных AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест подключения AstroGenix</h1>";

// Тест 1: Подключение к config файлам
echo "<h2>1. Тест подключения файлов конфигурации</h2>";

try {
    require_once 'config/config.php';
    echo "<p>✅ config/config.php подключен успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения config/config.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'config/database.php';
    echo "<p>✅ config/database.php подключен успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения config/database.php: " . $e->getMessage() . "</p>";
}

// Тест 2: Подключение к базе данных
echo "<h2>2. Тест подключения к базе данных</h2>";

try {
    $db = getDB();
    echo "<p>✅ Подключение к базе данных успешно</p>";
    
    // Проверяем существование базы данных
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "<p>📊 Текущая база данных: <strong>" . $result['db_name'] . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    echo "<p>💡 Убедитесь, что:</p>";
    echo "<ul>";
    echo "<li>MySQL запущен в OSPanel</li>";
    echo "<li>База данных 'astrogenix_eco_mining' создана</li>";
    echo "<li>Настройки в config/database.php корректны</li>";
    echo "</ul>";
}

// Тест 3: Проверка таблиц
echo "<h2>3. Проверка таблиц в базе данных</h2>";

if (isset($db)) {
    $required_tables = [
        'users' => 'Пользователи',
        'mining_packages' => 'Майнинг-пакеты',
        'site_settings' => 'Настройки сайта',
        'referrals' => 'Рефералы',
        'eco_news' => 'Экологические новости'
    ];
    
    foreach ($required_tables as $table => $description) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '$table'");
            if ($stmt->fetch()) {
                // Проверяем количество записей
                $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                echo "<p>✅ Таблица <strong>$table</strong> ($description) существует - $count записей</p>";
            } else {
                echo "<p>⚠️ Таблица <strong>$table</strong> ($description) не найдена</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Ошибка проверки таблицы $table: " . $e->getMessage() . "</p>";
        }
    }
}

// Тест 4: Подключение функций
echo "<h2>4. Тест подключения функций</h2>";

try {
    require_once 'includes/functions.php';
    echo "<p>✅ includes/functions.php подключен успешно</p>";
    
    // Тестируем основные функции
    if (function_exists('getAllMiningPackages')) {
        echo "<p>✅ Функция getAllMiningPackages() доступна</p>";
    } else {
        echo "<p>❌ Функция getAllMiningPackages() не найдена</p>";
    }
    
    if (function_exists('getEcoNews')) {
        echo "<p>✅ Функция getEcoNews() доступна</p>";
    } else {
        echo "<p>❌ Функция getEcoNews() не найдена</p>";
    }
    
    if (function_exists('generateReferralCode')) {
        echo "<p>✅ Функция generateReferralCode() доступна</p>";
    } else {
        echo "<p>❌ Функция generateReferralCode() не найдена</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения функций: " . $e->getMessage() . "</p>";
}

// Тест 5: Проверка настроек PHP
echo "<h2>5. Проверка настроек PHP</h2>";

echo "<p>📋 Версия PHP: <strong>" . PHP_VERSION . "</strong></p>";

$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ Расширение <strong>$ext</strong> загружено</p>";
    } else {
        echo "<p>❌ Расширение <strong>$ext</strong> не загружено</p>";
    }
}

// Тест 6: Проверка файловой структуры
echo "<h2>6. Проверка файловой структуры</h2>";

$required_files = [
    'index.php' => 'Главная страница',
    'pages/register.php' => 'Страница регистрации',
    'pages/login.php' => 'Страница входа',
    'admin/index.php' => 'Админ-панель',
    'assets/css/eco-animations.css' => 'CSS анимации',
    'assets/js/eco-animations.js' => 'JavaScript анимации'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p>✅ Файл <strong>$file</strong> ($description) существует</p>";
    } else {
        echo "<p>❌ Файл <strong>$file</strong> ($description) не найден</p>";
    }
}

// Итоговый результат
echo "<h2>🎯 Результат тестирования</h2>";

if (isset($db) && function_exists('getAllMiningPackages')) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Система готова к работе!</h3>";
    echo "<p>Все основные компоненты работают корректно.</p>";
    echo "<p><strong>Следующие шаги:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Перейти на главную страницу</a></li>";
    echo "<li><a href='pages/register.php'>Зарегистрировать первого пользователя</a></li>";
    echo "<li><a href='setup_database.php'>Настроить базу данных</a> (если нужно)</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Требуется настройка</h3>";
    echo "<p>Обнаружены проблемы, которые нужно исправить:</p>";
    echo "<ul>";
    if (!isset($db)) {
        echo "<li>Настройте подключение к базе данных</li>";
    }
    echo "<li>Импортируйте схему БД из файла database/astrogenix_eco_mining.sql</li>";
    echo "<li>Проверьте права доступа к файлам</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время выполнения теста: " . date('Y-m-d H:i:s') . "</small></p>";
?>
