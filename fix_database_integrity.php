<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Исправление целостности базы данных</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-4xl mx-auto p-8'>
    <h1 class='text-3xl font-bold text-gray-900 mb-8'>🔧 Исправление целостности базы данных</h1>";

try {
    $db = getDB();
    
    // Required tables with their creation SQL
    $required_tables = [
        'users' => "
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                balance DECIMAL(15,2) DEFAULT 0.00,
                is_admin BOOLEAN DEFAULT FALSE,
                role ENUM('user', 'admin') DEFAULT 'user',
                status ENUM('active', 'suspended') DEFAULT 'active',
                verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                reset_token VARCHAR(255) NULL,
                reset_expires DATETIME NULL
            )",
        'investments' => "
            CREATE TABLE investments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                category ENUM('real_estate', 'stocks', 'crypto', 'bonds', 'commodities', 'forex', 'other') NOT NULL DEFAULT 'real_estate',
                min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
                max_amount DECIMAL(15,2) NULL,
                monthly_rate DECIMAL(5,2) NOT NULL,
                duration_months INT NULL,
                image_url VARCHAR(500),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        'user_investments' => "
            CREATE TABLE user_investments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                investment_id INT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                monthly_rate DECIMAL(5,2) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                total_profit DECIMAL(15,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
            )",
        'transactions' => "
            CREATE TABLE transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                type ENUM('deposit', 'withdrawal') NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                transaction_hash VARCHAR(255) NULL,
                wallet_address VARCHAR(255) NULL,
                screenshot_path VARCHAR(500) NULL,
                admin_notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )",
        'daily_profits' => "
            CREATE TABLE daily_profits (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_investment_id INT NOT NULL,
                profit_amount DECIMAL(15,2) NOT NULL,
                profit_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
            )",
        'blog_posts' => "
            CREATE TABLE blog_posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                content TEXT NOT NULL,
                excerpt TEXT,
                category VARCHAR(50) DEFAULT 'general',
                tags TEXT,
                meta_title VARCHAR(255),
                meta_description TEXT,
                image_url VARCHAR(500),
                is_published BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        'contact_messages' => "
            CREATE TABLE contact_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        'site_settings' => "
            CREATE TABLE site_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        'balance_changes' => "
            CREATE TABLE balance_changes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                admin_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                operation ENUM('add', 'subtract') NOT NULL,
                notes TEXT,
                old_balance DECIMAL(10,2) NOT NULL,
                new_balance DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
            )",
        'user_verifications' => "
            CREATE TABLE user_verifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100) NOT NULL,
                birth_date DATE NOT NULL,
                passport_photo_path VARCHAR(500) NOT NULL,
                status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
                admin_notes TEXT NULL,
                submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                processed_by INT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
            )"
    ];
    
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка существующих таблиц</h2>";
    
    $missing_tables = [];
    $existing_tables = [];
    
    foreach ($required_tables as $table_name => $create_sql) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table_name]);
        
        if ($stmt->fetch()) {
            $existing_tables[] = $table_name;
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Таблица '$table_name' существует</p>";
        } else {
            $missing_tables[] = $table_name;
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Таблица '$table_name' отсутствует</p>";
        }
    }
    
    echo "</div>";
    
    if (!empty($missing_tables)) {
        echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6'>
                <h2 class='text-xl font-bold text-yellow-900 mb-4'>Создание отсутствующих таблиц</h2>";
        
        // Disable foreign key checks temporarily
        $db->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        foreach ($missing_tables as $table_name) {
            try {
                $db->exec($required_tables[$table_name]);
                echo "<p class='text-green-600'><i class='fas fa-plus mr-2'></i>Создана таблица '$table_name'</p>";
            } catch (Exception $e) {
                echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка создания таблицы '$table_name': " . $e->getMessage() . "</p>";
            }
        }
        
        // Re-enable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        echo "</div>";
    }
    
    // Create default admin user if users table is empty
    $stmt = $db->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetch()['count'];
    
    if ($user_count == 0) {
        echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6'>
                <h2 class='text-xl font-bold text-blue-900 mb-4'>Создание администратора по умолчанию</h2>";
        
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, email, password_hash, is_admin, role, status, balance) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        if ($stmt->execute(['admin', '<EMAIL>', $admin_password, 1, 'admin', 'active', 0.00])) {
            echo "<p class='text-blue-600'><i class='fas fa-user-plus mr-2'></i>Создан администратор: admin / admin123</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка создания администратора</p>";
        }
        
        echo "</div>";
    }
    
    // Insert default site settings
    $stmt = $db->query("SELECT COUNT(*) as count FROM site_settings");
    $settings_count = $stmt->fetch()['count'];
    
    if ($settings_count == 0) {
        echo "<div class='bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6'>
                <h2 class='text-xl font-bold text-purple-900 mb-4'>Создание настроек сайта по умолчанию</h2>";
        
        $default_settings = [
            ['site_name', 'AstroGenix', 'text'],
            ['site_description', 'The world\'s leading eco-friendly cryptocurrency mining platform powered by 100% renewable energy', 'text'],
            ['contact_email', '<EMAIL>', 'text'],
            ['contact_phone', '******-0123', 'text'],
            ['company_address', '123 Green Energy Drive, Austin, TX 78701', 'text'],
            ['usdt_wallet_address', 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', 'text'],
            ['facebook_url', '', 'text'],
            ['twitter_url', '', 'text'],
            ['instagram_url', '', 'text'],
            ['linkedin_url', '', 'text'],
            ['telegram_url', '', 'text'],
            ['maintenance_mode', '0', 'boolean'],
            // Дополнительные настройки AstroGenix
            ['min_investment', '50', 'number'],
            ['max_investment', '50000', 'number'],
            ['min_withdrawal', '10', 'number'],
            ['max_withdrawal', '10000', 'number'],
            ['referral_commission', '10', 'number'],
            ['profit_commission', '5', 'number'],
            ['withdrawal_fee', '2', 'number'],
            ['registration_bonus', '5', 'number'],
            ['platform_name', 'AstroGenix', 'text'],
            ['support_email', '<EMAIL>', 'text'],
            ['platform_description', 'Экологическая майнинг-платформа на устойчивой энергии', 'text'],
            ['registration_enabled', '1', 'boolean'],
            ['kyc_required', '0', 'boolean'],
            ['usdt_deposit_address', 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', 'text'],
            ['kyc_verification_required', '1', 'boolean']
        ];
        
        foreach ($default_settings as $setting) {
            $stmt = $db->prepare("INSERT INTO site_settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
            if ($stmt->execute($setting)) {
                echo "<p class='text-purple-600'><i class='fas fa-cog mr-2'></i>Создана настройка: {$setting[0]}</p>";
            }
        }
        
        echo "</div>";
    }
    
    // Create upload directories
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'>Создание директорий для загрузок</h2>";
    
    $upload_dirs = ['uploads', 'uploads/transactions', 'uploads/blog', 'uploads/verifications'];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<p class='text-green-600'><i class='fas fa-folder-plus mr-2'></i>Создана директория: $dir</p>";
            } else {
                echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка создания директории: $dir</p>";
            }
        } else {
            echo "<p class='text-blue-600'><i class='fas fa-folder mr-2'></i>Директория уже существует: $dir</p>";
        }
    }
    
    echo "</div>";
    
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Исправление завершено!</h2>
            <p class='text-green-700 mb-4'>Все необходимые таблицы и настройки созданы.</p>
            <div class='flex space-x-4'>
                <a href='test_improvements.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg'>
                    <i class='fas fa-vial mr-2'></i>Запустить тесты
                </a>
                <a href='admin/index.php' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка</h2>
            <p class='text-red-700'>Ошибка подключения к базе данных: " . $e->getMessage() . "</p>
          </div>";
}

echo "</div></body></html>";
?>
