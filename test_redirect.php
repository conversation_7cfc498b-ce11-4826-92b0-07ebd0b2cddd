<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест функции Redirect - AstroGenix</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">🔄 Тест функции Redirect</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            // Include required files
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            if (isset($_GET['test']) && $_GET['test'] === 'redirect') {
                echo '<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">';
                echo '<h3 class="font-semibold text-green-900 mb-2">✅ Тест перенаправления</h3>';
                echo '<p class="text-sm text-green-800">Если вы видите это сообщение, значит функция redirect() работает корректно!</p>';
                echo '<p class="text-sm text-green-800 mt-2">Через 3 секунды вы будете перенаправлены обратно...</p>';
                echo '</div>';
                
                // Redirect back after 3 seconds using JavaScript
                echo '<script>setTimeout(function() { window.location.href = "test_redirect.php"; }, 3000);</script>';
            } else {
                ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-blue-900 mb-2">🧪 Тестирование функции redirect()</h3>
                    <p class="text-sm text-blue-800 mb-3">
                        Эта страница проверяет, что функция redirect() работает без ошибок "Cannot redeclare function".
                    </p>
                    <p class="text-sm text-blue-800">
                        Нажмите кнопку ниже, чтобы протестировать перенаправление.
                    </p>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">📋 Проверка функции:</h4>
                        <div class="space-y-2 text-sm">
                            <?php
                            if (function_exists('redirect')) {
                                echo '<div class="flex items-center text-green-600">';
                                echo '<span class="mr-2">✅</span>';
                                echo '<span>Функция redirect() найдена</span>';
                                echo '</div>';
                            } else {
                                echo '<div class="flex items-center text-red-600">';
                                echo '<span class="mr-2">❌</span>';
                                echo '<span>Функция redirect() не найдена</span>';
                                echo '</div>';
                            }
                            
                            // Check if function is declared multiple times by checking reflection
                            try {
                                $reflection = new ReflectionFunction('redirect');
                                $file = $reflection->getFileName();
                                $line = $reflection->getStartLine();
                                
                                echo '<div class="flex items-center text-blue-600">';
                                echo '<span class="mr-2">📍</span>';
                                echo '<span>Функция определена в: ' . basename($file) . ' (строка ' . $line . ')</span>';
                                echo '</div>';
                            } catch (Exception $e) {
                                echo '<div class="flex items-center text-red-600">';
                                echo '<span class="mr-2">❌</span>';
                                echo '<span>Ошибка получения информации о функции</span>';
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="test_redirect.php?test=redirect" 
                           class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block">
                            🔄 Тестировать Redirect
                        </a>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-900 mb-2">⚠️ Примечание:</h4>
                        <p class="text-sm text-yellow-800">
                            Если при нажатии на кнопку выше возникает ошибка "Cannot redeclare function", 
                            значит функция redirect() всё ещё дублируется в файлах.
                        </p>
                    </div>
                </div>
                <?php
            }
            ?>
            
            <div class="mt-8 text-center">
                <a href="check_errors.php" class="text-blue-500 hover:text-blue-600 font-medium">
                    ← Вернуться к проверке ошибок
                </a>
            </div>
        </div>
    </div>
</body>
</html>
