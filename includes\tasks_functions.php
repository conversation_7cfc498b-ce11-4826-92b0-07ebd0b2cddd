<?php
/**
 * AstroGenix Tasks/Missions System Functions
 * Complete task management system for eco mining platform
 */

require_once __DIR__ . '/../config/database.php';

/**
 * Get all active tasks
 */
function getActiveTasks($category = null) {
    global $pdo;
    
    try {
        $sql = "
            SELECT * FROM tasks 
            WHERE is_active = 1 
            AND (start_date IS NULL OR start_date <= NOW()) 
            AND (end_date IS NULL OR end_date >= NOW())
        ";
        
        if ($category) {
            $sql .= " AND category = ?";
            $stmt = $pdo->prepare($sql . " ORDER BY sort_order ASC, difficulty ASC");
            $stmt->execute([$category]);
        } else {
            $stmt = $pdo->prepare($sql . " ORDER BY category, sort_order ASC, difficulty ASC");
            $stmt->execute();
        }
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get active tasks error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user's task progress
 */
function getUserTaskProgress($user_id, $task_id = null) {
    global $pdo;
    
    try {
        if ($task_id) {
            $stmt = $pdo->prepare("
                SELECT utp.*, t.title, t.description, t.requirements, t.reward_amount, t.icon, t.difficulty
                FROM user_task_progress utp
                JOIN tasks t ON utp.task_id = t.id
                WHERE utp.user_id = ? AND utp.task_id = ?
            ");
            $stmt->execute([$user_id, $task_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $stmt = $pdo->prepare("
                SELECT utp.*, t.title, t.description, t.requirements, t.reward_amount, t.icon, t.difficulty, t.category
                FROM user_task_progress utp
                JOIN tasks t ON utp.task_id = t.id
                WHERE utp.user_id = ?
                ORDER BY t.category, t.sort_order ASC
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        error_log("Get user task progress error: " . $e->getMessage());
        return $task_id ? false : [];
    }
}

/**
 * Initialize user task progress for new tasks
 */
function initializeUserTasks($user_id) {
    global $pdo;
    
    try {
        // Get all active tasks that user doesn't have progress for
        $stmt = $pdo->prepare("
            SELECT t.id, t.requirements
            FROM tasks t
            LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
            WHERE t.is_active = 1 
            AND utp.id IS NULL
            AND (t.start_date IS NULL OR t.start_date <= NOW()) 
            AND (t.end_date IS NULL OR t.end_date >= NOW())
        ");
        $stmt->execute([$user_id]);
        $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($tasks as $task) {
            $requirements = json_decode($task['requirements'], true);
            $required_progress = $requirements['count'] ?? $requirements['days'] ?? $requirements['min_amount'] ?? 1;
            
            $stmt = $pdo->prepare("
                INSERT INTO user_task_progress (user_id, task_id, required_progress, status)
                VALUES (?, ?, ?, 'not_started')
            ");
            $stmt->execute([$user_id, $task['id'], $required_progress]);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Initialize user tasks error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update task progress
 */
function updateTaskProgress($user_id, $task_type, $data = []) {
    global $pdo;
    
    try {
        // Get relevant tasks for this type
        $stmt = $pdo->prepare("
            SELECT t.*, utp.id as progress_id, utp.current_progress, utp.required_progress, utp.status
            FROM tasks t
            LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
            WHERE t.is_active = 1 AND t.task_type = ?
            AND (t.start_date IS NULL OR t.start_date <= NOW()) 
            AND (t.end_date IS NULL OR t.end_date >= NOW())
        ");
        $stmt->execute([$user_id, $task_type]);
        $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($tasks as $task) {
            $requirements = json_decode($task['requirements'], true);
            $should_update = false;
            $new_progress = $task['current_progress'] ?? 0;
            
            // Initialize progress if not exists
            if (!$task['progress_id']) {
                initializeUserTasks($user_id);
                continue;
            }
            
            // Skip if already completed
            if ($task['status'] === 'completed' || $task['status'] === 'claimed') {
                continue;
            }
            
            // Check task type and update progress accordingly
            switch ($task_type) {
                case 'investment':
                    if (isset($data['amount']) && $data['amount'] >= ($requirements['min_amount'] ?? 0)) {
                        if (isset($requirements['count'])) {
                            $new_progress = $task['current_progress'] + 1;
                            $should_update = true;
                        } elseif (isset($requirements['total_invested'])) {
                            // Get user's total invested amount
                            $total_invested = getUserTotalInvested($user_id);
                            $new_progress = $total_invested;
                            $should_update = true;
                        }
                    }
                    break;
                    
                case 'referral':
                    if (isset($requirements['count']) || isset($requirements['active_referrals'])) {
                        $referral_stats = getReferralStats($user_id);
                        if (isset($requirements['active_referrals'])) {
                            $new_progress = $referral_stats['active_referrals'] ?? 0;
                        } else {
                            $new_progress = $referral_stats['total_referrals'] ?? 0;
                        }
                        $should_update = true;
                    }
                    break;
                    
                case 'login_streak':
                    if (isset($data['streak_days'])) {
                        $new_progress = $data['streak_days'];
                        $should_update = true;
                    }
                    break;
                    
                case 'milestone':
                    if (isset($requirements['co2_saved'])) {
                        $user = getUserById($user_id);
                        $new_progress = $user['total_co2_saved'] ?? 0;
                        $should_update = true;
                    }
                    break;
            }
            
            if ($should_update) {
                // Update progress
                $status = $new_progress >= $task['required_progress'] ? 'completed' : 'in_progress';
                
                $stmt = $pdo->prepare("
                    UPDATE user_task_progress 
                    SET current_progress = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$new_progress, $status, $task['progress_id']]);
                
                // If task completed, trigger notification
                if ($status === 'completed' && $task['status'] !== 'completed') {
                    notifyTaskCompleted($user_id, $task['id']);
                }
            }
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Update task progress error: " . $e->getMessage());
        return false;
    }
}

/**
 * Claim task reward
 */
function claimTaskReward($user_id, $task_id) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Get task and progress
        $stmt = $pdo->prepare("
            SELECT t.*, utp.status, utp.current_progress, utp.required_progress
            FROM tasks t
            JOIN user_task_progress utp ON t.id = utp.task_id
            WHERE t.id = ? AND utp.user_id = ? AND utp.status = 'completed'
        ");
        $stmt->execute([$task_id, $user_id]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$task) {
            throw new Exception("Task not found or not completed");
        }
        
        // Add reward to user balance
        $stmt = $pdo->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$task['reward_amount'], $user_id]);
        
        // Update task progress to claimed
        $stmt = $pdo->prepare("
            UPDATE user_task_progress 
            SET status = 'claimed', claimed_at = CURRENT_TIMESTAMP, reward_claimed = ?
            WHERE task_id = ? AND user_id = ?
        ");
        $stmt->execute([$task['reward_amount'], $task_id, $user_id]);
        
        // Log completion
        $stmt = $pdo->prepare("
            INSERT INTO task_completions (user_id, task_id, reward_amount, reward_type)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $task_id, $task['reward_amount'], $task['reward_type']]);
        
        $pdo->commit();
        return ['success' => true, 'reward' => $task['reward_amount']];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Claim task reward error: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get user's total invested amount
 */
function getUserTotalInvested($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as total
            FROM user_mining_investments 
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Get user total invested error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Notify user of task completion
 */
function notifyTaskCompleted($user_id, $task_id) {
    // This could send email, push notification, etc.
    // For now, we'll just log it
    error_log("Task completed: User $user_id completed task $task_id");
}

/**
 * Get task categories
 */
function getTaskCategories() {
    return [
        'investment' => 'Investment Tasks',
        'referral' => 'Referral Tasks',
        'engagement' => 'Daily Engagement',
        'environmental' => 'Environmental Impact',
        'special' => 'Special Events'
    ];
}

/**
 * Get all tasks (including inactive ones) for admin
 */
function getAllTasks() {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT * FROM tasks ORDER BY category, sort_order ASC, created_at DESC");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Get all tasks error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get task statistics for admin dashboard
 */
function getTaskStatistics() {
    global $pdo;

    try {
        $stats = [];

        // Total tasks
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tasks");
        $stmt->execute();
        $stats['total_tasks'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Active tasks
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tasks WHERE is_active = 1");
        $stmt->execute();
        $stats['active_tasks'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Total completions
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM task_completions");
        $stmt->execute();
        $stats['total_completions'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Total rewards paid
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(reward_amount), 0) as total FROM task_completions");
        $stmt->execute();
        $stats['total_rewards'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        return $stats;
    } catch (PDOException $e) {
        error_log("Get task statistics error: " . $e->getMessage());
        return [
            'total_tasks' => 0,
            'active_tasks' => 0,
            'total_completions' => 0,
            'total_rewards' => 0
        ];
    }
}

/**
 * Get completion count for a specific task
 */
function getTaskCompletionCount($task_id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM task_completions WHERE task_id = ?");
        $stmt->execute([$task_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        error_log("Get task completion count error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Create a new task
 */
function createTask($data) {
    global $pdo;

    try {
        // Validate requirements JSON
        $requirements = json_decode($data['requirements'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'message' => 'Invalid JSON in requirements'];
        }

        $stmt = $pdo->prepare("
            INSERT INTO tasks (title, description, task_type, requirements, reward_amount,
                             reward_type, is_active, is_repeatable, icon, difficulty, category, sort_order)
            VALUES (?, ?, ?, ?, ?, 'usdt', ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $data['title'],
            $data['description'],
            $data['task_type'],
            $data['requirements'],
            $data['reward_amount'],
            isset($data['is_active']) ? 1 : 0,
            isset($data['is_repeatable']) ? 1 : 0,
            $data['icon'] ?? 'fas fa-tasks',
            $data['difficulty'] ?? 'easy',
            $data['category'] ?? 'general',
            $data['sort_order'] ?? 0
        ]);

        return ['success' => $result];
    } catch (PDOException $e) {
        error_log("Create task error: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Update an existing task
 */
function updateTask($task_id, $data) {
    global $pdo;

    try {
        // Validate requirements JSON
        $requirements = json_decode($data['requirements'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'message' => 'Invalid JSON in requirements'];
        }

        $stmt = $pdo->prepare("
            UPDATE tasks SET
                title = ?, description = ?, task_type = ?, requirements = ?, reward_amount = ?,
                is_active = ?, is_repeatable = ?, icon = ?, difficulty = ?, category = ?, sort_order = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $result = $stmt->execute([
            $data['title'],
            $data['description'],
            $data['task_type'],
            $data['requirements'],
            $data['reward_amount'],
            isset($data['is_active']) ? 1 : 0,
            isset($data['is_repeatable']) ? 1 : 0,
            $data['icon'] ?? 'fas fa-tasks',
            $data['difficulty'] ?? 'easy',
            $data['category'] ?? 'general',
            $data['sort_order'] ?? 0,
            $task_id
        ]);

        return ['success' => $result];
    } catch (PDOException $e) {
        error_log("Update task error: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Delete a task
 */
function deleteTask($task_id) {
    global $pdo;

    try {
        $pdo->beginTransaction();

        // Delete related progress and completions first
        $stmt = $pdo->prepare("DELETE FROM user_task_progress WHERE task_id = ?");
        $stmt->execute([$task_id]);

        $stmt = $pdo->prepare("DELETE FROM task_completions WHERE task_id = ?");
        $stmt->execute([$task_id]);

        // Delete the task
        $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
        $result = $stmt->execute([$task_id]);

        $pdo->commit();
        return ['success' => $result];
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Delete task error: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}
?>
