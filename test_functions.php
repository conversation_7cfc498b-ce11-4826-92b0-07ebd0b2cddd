<?php
/**
 * Тест функций AstroGenix - проверка дублирования
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест функций AstroGenix</h1>";

// Тест 1: Подключение основных файлов
echo "<h2>1. Тест подключения файлов</h2>";

try {
    require_once 'config/config.php';
    echo "<p>✅ config/config.php подключен</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка config/config.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'config/database.php';
    echo "<p>✅ config/database.php подключен</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка config/database.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'includes/functions.php';
    echo "<p>✅ includes/functions.php подключен (включает все остальные)</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка includes/functions.php: " . $e->getMessage() . "</p>";
    exit;
}

// Тест 2: Проверка существования ключевых функций
echo "<h2>2. Проверка функций</h2>";

$functions_to_check = [
    'getDB' => 'Подключение к БД',
    'getUserById' => 'Получение пользователя',
    'getDashboardStats' => 'Статистика дашборда',
    'getUserInvestments' => 'Инвестиции пользователя',
    'getUserTransactions' => 'Транзакции пользователя',
    'getAllMiningPackages' => 'Майнинг-пакеты',
    'createMiningInvestment' => 'Создание майнинг-инвестиции',
    'getMiningPackageById' => 'Получение пакета по ID',
    'generateReferralCode' => 'Генерация реферального кода',
    'getEcoNews' => 'Экологические новости',
    'addDailyProfit' => 'Добавление ежедневной прибыли (старая)',
    'addDailyMiningProfit' => 'Добавление майнинг-прибыли (новая)'
];

foreach ($functions_to_check as $function => $description) {
    if (function_exists($function)) {
        echo "<p>✅ <strong>$function()</strong> - $description</p>";
    } else {
        echo "<p>❌ <strong>$function()</strong> - $description НЕ НАЙДЕНА</p>";
    }
}

// Тест 3: Проверка подключения к БД
echo "<h2>3. Тест подключения к БД</h2>";

try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
    
    // Проверяем текущую БД
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "<p>📊 Текущая БД: <strong>" . $result['db_name'] . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
}

// Тест 4: Тест функций майнинга (если БД доступна)
if (isset($db)) {
    echo "<h2>4. Тест майнинг-функций</h2>";
    
    try {
        // Тест getAllMiningPackages
        if (function_exists('getAllMiningPackages')) {
            $packages = getAllMiningPackages(5);
            echo "<p>✅ getAllMiningPackages(): найдено " . count($packages) . " пакетов</p>";
        }
        
        // Тест getMiningPackageById
        if (function_exists('getMiningPackageById')) {
            $package = getMiningPackageById(1);
            if ($package) {
                echo "<p>✅ getMiningPackageById(1): пакет найден - " . htmlspecialchars($package['name']) . "</p>";
            } else {
                echo "<p>⚠️ getMiningPackageById(1): пакет не найден (возможно, нет данных)</p>";
            }
        }
        
        // Тест createMiningInvestment (только проверка существования)
        if (function_exists('createMiningInvestment')) {
            echo "<p>✅ createMiningInvestment(): функция доступна</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка тестирования майнинг-функций: " . $e->getMessage() . "</p>";
    }
}

// Тест 5: Тест функций дашборда
if (isset($db)) {
    echo "<h2>5. Тест функций дашборда</h2>";
    
    try {
        // Проверяем, есть ли пользователи
        $stmt = $db->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch()['count'];
        
        if ($user_count > 0) {
            // Берем первого пользователя
            $stmt = $db->query("SELECT id FROM users LIMIT 1");
            $user = $stmt->fetch();
            $user_id = $user['id'];
            
            // Тест getDashboardStats
            if (function_exists('getDashboardStats')) {
                $stats = getDashboardStats($user_id);
                echo "<p>✅ getDashboardStats($user_id): работает</p>";
                echo "<ul>";
                echo "<li>Баланс: " . $stats['balance'] . "</li>";
                echo "<li>Инвестировано: " . $stats['total_invested'] . "</li>";
                echo "<li>Заработано: " . $stats['total_profit'] . "</li>";
                echo "<li>Активных инвестиций: " . $stats['active_investments'] . "</li>";
                if (isset($stats['total_co2_saved'])) {
                    echo "<li>CO₂ сэкономлено: " . $stats['total_co2_saved'] . "</li>";
                }
                echo "</ul>";
            }
            
            // Тест getUserInvestments
            if (function_exists('getUserInvestments')) {
                $investments = getUserInvestments($user_id);
                echo "<p>✅ getUserInvestments($user_id): найдено " . count($investments) . " инвестиций</p>";
            }

            // Тест getUserTransactions
            if (function_exists('getUserTransactions')) {
                $transactions = getUserTransactions($user_id, 5);
                echo "<p>✅ getUserTransactions($user_id): найдено " . count($transactions) . " транзакций</p>";
            }
            
        } else {
            echo "<p>⚠️ Нет пользователей для тестирования дашборда</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка тестирования дашборда: " . $e->getMessage() . "</p>";
    }
}

// Итоговый результат
echo "<h2>🎯 Результат тестирования</h2>";

$all_functions_exist = true;
foreach ($functions_to_check as $function => $description) {
    if (!function_exists($function)) {
        $all_functions_exist = false;
        break;
    }
}

if ($all_functions_exist && isset($db)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Все функции работают корректно!</h3>";
    echo "<p>Дублирование функций устранено, система готова к работе.</p>";
    echo "<p><strong>Можно использовать:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Главная страница</a></li>";
    echo "<li><a href='pages/dashboard.php'>Дашборд</a></li>";
    echo "<li><a href='pages/mining-packages.php'>Майнинг-пакеты</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p>Некоторые функции недоступны или есть проблемы с БД.</p>";
    echo "<p>Рекомендации:</p>";
    echo "<ul>";
    echo "<li>Импортируйте схему БД из database/astrogenix_eco_mining.sql</li>";
    echo "<li>Проверьте настройки подключения к БД</li>";
    echo "<li>Убедитесь, что все файлы на месте</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . "</small></p>";
?>
