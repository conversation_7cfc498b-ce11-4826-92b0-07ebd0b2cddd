<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'Eco Mining Farms';
$page_description = 'Explore our exclusive collection of sustainable cryptocurrency mining farms powered by renewable energy';

// Get filter parameters
$category_filter = $_GET['category'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';

// Get all investments
$investments = getAllInvestments(true);

// Apply category filter
if ($category_filter) {
    $investments = array_filter($investments, function($investment) use ($category_filter) {
        return $investment['category'] === $category_filter;
    });
}

// Sort investments
usort($investments, function($a, $b) use ($sort_by, $sort_order) {
    $result = 0;
    
    switch ($sort_by) {
        case 'price':
            $result = $a['price'] <=> $b['price'];
            break;
        case 'monthly_rate_max':
            $result = $a['monthly_rate_max'] <=> $b['monthly_rate_max'];
            break;
        case 'return_period_months':
            $result = $a['return_period_months'] <=> $b['return_period_months'];
            break;
        default:
            $result = strtotime($a['created_at']) <=> strtotime($b['created_at']);
    }
    
    return $sort_order === 'DESC' ? -$result : $result;
});

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-green-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-slate-900 via-green-900 to-slate-800 text-white py-16 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
            <div class="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="mb-6">
                    <span class="inline-block px-6 py-3 bg-white bg-opacity-15 rounded-full text-sm font-medium mb-4 backdrop-blur-sm border border-white border-opacity-20 shadow-lg">
                        💎 Premium Opportunities
                    </span>
                </div>
                <h1 class="text-4xl lg:text-5xl font-bold mb-4">Investment Opportunities</h1>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Discover exclusive luxury assets that generate consistent returns.
                    From premium real estate to luxury yachts, find your perfect investment.
                </p>
            </div>
        </div>
    </section>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Filters and Sorting -->
        <div class="bg-white rounded-xl card-shadow p-6 mb-8 border border-gray-100">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
                <!-- Category Filter -->
                <div class="flex flex-wrap gap-2">
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => ''])); ?>"
                       class="px-4 py-2 rounded-lg font-semibold transition-all duration-300 <?php echo !$category_filter ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'; ?>">
                        All Categories
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'real_estate'])); ?>"
                       class="px-4 py-2 rounded-lg font-semibold transition-all duration-300 <?php echo $category_filter === 'real_estate' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'; ?>">
                        Real Estate
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['category' => 'yacht'])); ?>"
                       class="px-4 py-2 rounded-lg font-semibold transition-all duration-300 <?php echo $category_filter === 'yacht' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'; ?>">
                        Yachts
                    </a>
                </div>

                <!-- Sort Options -->
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700 font-medium">Sort by:</span>
                    <select onchange="updateSort(this.value)" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900">
                        <option value="created_at-DESC" <?php echo ($sort_by === 'created_at' && $sort_order === 'DESC') ? 'selected' : ''; ?>>Newest First</option>
                        <option value="price-ASC" <?php echo ($sort_by === 'price' && $sort_order === 'ASC') ? 'selected' : ''; ?>>Price: Low to High</option>
                        <option value="price-DESC" <?php echo ($sort_by === 'price' && $sort_order === 'DESC') ? 'selected' : ''; ?>>Price: High to Low</option>
                        <option value="monthly_rate_max-DESC" <?php echo ($sort_by === 'monthly_rate_max' && $sort_order === 'DESC') ? 'selected' : ''; ?>>Highest Returns</option>
                        <option value="return_period_months-ASC" <?php echo ($sort_by === 'return_period_months' && $sort_order === 'ASC') ? 'selected' : ''; ?>>Shortest Term</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Investment Grid -->
        <?php if (empty($investments)): ?>
            <div class="text-center py-16">
                <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-700 mb-2">No investments found</h3>
                <p class="text-gray-500 mb-6">Try adjusting your filters or check back later for new opportunities</p>
                <a href="?" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">Clear Filters</a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($investments as $investment): ?>
                    <div class="bg-white rounded-2xl card-shadow hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100">
                        <div class="relative">
                            <img src="<?php echo getEscapedInvestmentImage($investment); ?>"
                                 alt="<?php echo htmlspecialchars($investment['title']); ?>"
                                 class="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300">

                            <!-- Category Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                                    <?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?>
                                </span>
                            </div>

                            <!-- Capital Return Badge -->
                            <?php if ($investment['capital_return']): ?>
                                <div class="absolute top-4 right-4">
                                    <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                                        Capital Return
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="p-6">
                            <div class="mb-4">
                                <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                                    <?php echo htmlspecialchars($investment['title']); ?>
                                </h3>
                                <p class="text-gray-600 flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2 text-blue-500"></i>
                                    <?php echo htmlspecialchars($investment['location']); ?>
                                </p>
                            </div>

                            <div class="space-y-3 mb-6">
                                <!-- Investment Amount -->
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Investment Amount</span>
                                    <span class="text-xl font-bold text-gray-900"><?php echo formatCurrency($investment['price']); ?></span>
                                </div>

                                <!-- Monthly Returns -->
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Monthly Returns</span>
                                    <span class="text-lg font-bold text-green-600">
                                        <?php echo formatPercentage($investment['monthly_rate_min']); ?> - <?php echo formatPercentage($investment['monthly_rate_max']); ?>
                                    </span>
                                </div>

                                <!-- Return Period -->
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Return Period</span>
                                    <span class="font-semibold text-gray-900">
                                        <?php echo $investment['return_period_months']; ?> months
                                    </span>
                                </div>
                            </div>

                            <!-- Features Preview -->
                            <?php if ($investment['features']): ?>
                                <div class="mb-6">
                                    <p class="text-sm text-gray-500 line-clamp-2">
                                        <?php echo htmlspecialchars($investment['features']); ?>
                                    </p>
                                </div>
                            <?php endif; ?>

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                <a href="investment-detail.php?id=<?php echo $investment['id']; ?>"
                                   class="block w-full text-center bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    View Details
                                </a>

                                <?php if (isLoggedIn()): ?>
                                    <button onclick="openInvestModal(<?php echo $investment['id']; ?>, '<?php echo htmlspecialchars($investment['title']); ?>', <?php echo $investment['price']; ?>)"
                                            class="w-full bg-white border-2 border-blue-500 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg font-medium transition-all duration-300">
                                        Invest Now
                                    </button>
                                <?php else: ?>
                                    <a href="login.php"
                                       class="block w-full text-center bg-white border-2 border-blue-500 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg font-medium transition-all duration-300">
                                        Login to Invest
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Investment Benefits -->
        <div class="mt-16 bg-white rounded-2xl card-shadow p-8 border border-gray-100">
            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent text-center mb-8">Why Invest With Us?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Secure Assets</h3>
                    <p class="text-gray-600">All investments are backed by real, tangible assets with comprehensive insurance coverage.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Consistent Returns</h3>
                    <p class="text-gray-600">Generate monthly passive income with our carefully selected high-yield investments.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Expert Management</h3>
                    <p class="text-gray-600">Professional management team handles all aspects of your investment portfolio.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (isLoggedIn()): ?>
<!-- Investment Modal -->
<div id="investModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-dark-contrast">Make Investment</h3>
                <button onclick="closeInvestModal()" class="text-light-contrast hover:text-medium-contrast">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div id="investmentDetails" class="mb-6">
                <!-- Investment details will be populated by JavaScript -->
            </div>
            
            <form method="POST" action="process-investment.php">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="investment_id" id="modal_investment_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-dark-contrast mb-2">Investment Amount</label>
                    <input type="number" name="amount" id="investment_amount" step="0.01" min="<?php echo MIN_INVESTMENT_AMOUNT; ?>" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue">
                    <p class="text-sm text-light-contrast mt-1">
                        Available balance: <?php echo isLoggedIn() ? formatCurrency(getUserById(getCurrentUserId())['balance']) : '$0.00'; ?>
                    </p>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-4">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        Your investment will start generating daily returns immediately after confirmation.
                    </p>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeInvestModal()"
                            class="flex-1 btn-outline">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 btn-primary text-white">
                        Confirm Investment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function updateSort(value) {
    const [sort, order] = value.split('-');
    const url = new URL(window.location);
    url.searchParams.set('sort', sort);
    url.searchParams.set('order', order);
    window.location.href = url.toString();
}

<?php if (isLoggedIn()): ?>
function openInvestModal(investmentId, title, price) {
    document.getElementById('modal_investment_id').value = investmentId;
    document.getElementById('investment_amount').max = price;
    
    document.getElementById('investmentDetails').innerHTML = `
        <div class="text-center">
            <h4 class="font-bold text-lg text-primary mb-2">${title}</h4>
            <p class="text-gray-600">Maximum Investment: ${formatCurrency(price)}</p>
        </div>
    `;
    
    document.getElementById('investModal').classList.remove('hidden');
}

function closeInvestModal() {
    document.getElementById('investModal').classList.add('hidden');
}

function formatCurrency(amount) {
    return '$' + parseFloat(amount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
}

// Close modal when clicking outside
document.getElementById('investModal').addEventListener('click', function(e) {
    if (e.target === this) closeInvestModal();
});
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
