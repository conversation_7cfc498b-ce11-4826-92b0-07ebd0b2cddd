<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// Проверяем права администратора
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit;
}

$page_title = 'Генератор тестовых данных - AstroGenix Admin';

// Обработка генерации данных
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'generate_users':
                $count = intval($_POST['user_count'] ?? 10);
                $result = generateFakeUsers($count);
                $message = "Создано $result пользователей";
                $message_type = 'success';
                break;
                
            case 'generate_transactions':
                $count = intval($_POST['transaction_count'] ?? 20);
                $result = generateFakeTransactions($count);
                $message = "Создано $result транзакций";
                $message_type = 'success';
                break;
                
            case 'generate_mining':
                $count = intval($_POST['mining_count'] ?? 15);
                $result = generateFakeMiningActivity($count);
                $message = "Создано $result майнинг-инвестиций";
                $message_type = 'success';
                break;
                
            case 'generate_referrals':
                $count = intval($_POST['referral_count'] ?? 10);
                $result = generateFakeReferralActivity($count);
                $message = "Создано $result реферальных связей";
                $message_type = 'success';
                break;

            case 'generate_daily_activity':
                $days = intval($_POST['days_count'] ?? 7);
                $result = generateDailyActivity($days);
                $message = "Сгенерирована активность за $days дней";
                $message_type = 'success';
                break;

            case 'simulate_live_activity':
                $duration = intval($_POST['duration'] ?? 60); // minutes
                $result = simulateLiveActivity($duration);
                $message = "Запущена симуляция активности на $duration минут";
                $message_type = 'success';
                break;

            case 'generate_all':
                $users = generateFakeUsers(25);
                $transactions = generateFakeTransactions(50);
                $mining = generateFakeMiningActivity(30);
                $referrals = generateFakeReferralActivity(15);
                $message = "Создано: $users пользователей, $transactions транзакций, $mining майнинг-инвестиций, $referrals рефералов";
                $message_type = 'success';
                break;
                
            case 'clear_fake_data':
                clearFakeData();
                $message = "Все тестовые данные удалены";
                $message_type = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = "Ошибка: " . $e->getMessage();
        $message_type = 'error';
    }
}

include '../includes/admin_header.php';
?>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">🎲 Генератор тестовых данных</h1>
                    <p class="text-gray-600 mt-2">Создание реалистичных данных для тестирования платформы</p>
                </div>
                <a href="index.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Назад
                </a>
            </div>
        </div>

        <!-- Message -->
        <?php if ($message): ?>
        <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 border border-green-200 text-green-800' : 'bg-red-100 border border-red-200 text-red-800'; ?>">
            <div class="flex items-center">
                <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Fake Users Generator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">👥 Генератор пользователей</h2>
                    <p class="text-gray-600">Создание тестовых пользователей с реалистичными данными</p>
                </div>
                
                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="generate_users">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Количество пользователей</label>
                        <input type="number" name="user_count" value="10" min="1" max="100" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет создано:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Случайные имена пользователей</li>
                            <li>• Реалистичные email адреса</li>
                            <li>• Случайные даты регистрации</li>
                            <li>• Начальные балансы (0-1000 USDT)</li>
                            <li>• Реферальные коды</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-users mr-2"></i>Создать пользователей
                    </button>
                </form>
            </div>

            <!-- Fake Transactions Generator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">💰 Генератор транзакций</h2>
                    <p class="text-gray-600">Создание депозитов и выводов для тестирования</p>
                </div>
                
                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="generate_transactions">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Количество транзакций</label>
                        <input type="number" name="transaction_count" value="20" min="1" max="200" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет создано:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Депозиты (50-5000 USDT)</li>
                            <li>• Выводы (10-2000 USDT)</li>
                            <li>• Различные статусы (pending, approved, rejected)</li>
                            <li>• Случайные даты и времена</li>
                            <li>• Реалистичные кошельки</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-exchange-alt mr-2"></i>Создать транзакции
                    </button>
                </form>
            </div>

            <!-- Fake Mining Activity Generator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-violet-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">⚡ Генератор майнинг-активности</h2>
                    <p class="text-gray-600">Создание инвестиций и ежедневных прибылей</p>
                </div>
                
                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="generate_mining">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Количество инвестиций</label>
                        <input type="number" name="mining_count" value="15" min="1" max="100" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>
                    
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет создано:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Майнинг-инвестиции (100-10000 USDT)</li>
                            <li>• Ежедневные прибыли</li>
                            <li>• CO₂ статистика</li>
                            <li>• Различные майнинг-пакеты</li>
                            <li>• Реалистичные временные рамки</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-pickaxe mr-2"></i>Создать майнинг-активность
                    </button>
                </form>
            </div>

            <!-- Fake Referral Activity Generator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-pink-50 to-rose-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">👥 Генератор реферальной активности</h2>
                    <p class="text-gray-600">Создание реферальных связей и комиссий</p>
                </div>

                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="generate_referrals">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Количество реферальных связей</label>
                        <input type="number" name="referral_count" value="10" min="1" max="50"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                    </div>

                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет создано:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Реферальные связи между пользователями</li>
                            <li>• Активные рефералы с инвестициями</li>
                            <li>• Реферальные комиссии</li>
                            <li>• Бонусы за регистрацию</li>
                            <li>• Обновление рейтингов</li>
                        </ul>
                    </div>

                    <button type="submit" class="w-full bg-pink-500 hover:bg-pink-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-users mr-2"></i>Создать реферальную активность
                    </button>
                </form>
            </div>

            <!-- Daily Activity Simulator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-cyan-50 to-blue-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">📊 Симулятор ежедневной активности</h2>
                    <p class="text-gray-600">Генерация активности за несколько дней</p>
                </div>

                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="generate_daily_activity">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Количество дней</label>
                        <input type="number" name="days_count" value="7" min="1" max="30"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500">
                    </div>

                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет создано:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Ежедневные майнинг-прибыли</li>
                            <li>• Новые регистрации</li>
                            <li>• Транзакции по дням</li>
                            <li>• Обновление статистики</li>
                            <li>• Реалистичное распределение по времени</li>
                        </ul>
                    </div>

                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-calendar-alt mr-2"></i>Сгенерировать активность
                    </button>
                </form>
            </div>

            <!-- Live Activity Simulator -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-yellow-50 to-orange-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">⚡ Симулятор живой активности</h2>
                    <p class="text-gray-600">Имитация активности в реальном времени</p>
                </div>

                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="simulate_live_activity">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Продолжительность (минуты)</label>
                        <input type="number" name="duration" value="60" min="5" max="1440"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500">
                    </div>

                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-2">Что будет происходить:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Случайные регистрации пользователей</li>
                            <li>• Новые инвестиции</li>
                            <li>• Транзакции депозитов/выводов</li>
                            <li>• Обновление онлайн-статистики</li>
                            <li>• Имитация реального трафика</li>
                        </ul>
                    </div>

                    <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-play mr-2"></i>Запустить симуляцию
                    </button>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-red-50">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">🚀 Быстрые действия</h2>
                    <p class="text-gray-600">Массовые операции с данными</p>
                </div>
                
                <div class="p-6 space-y-4">
                    <form method="POST" class="w-full">
                        <input type="hidden" name="action" value="generate_all">
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-magic mr-2"></i>Создать все данные
                        </button>
                    </form>
                    
                    <div class="text-center text-sm text-gray-500">
                        Создаст 25 пользователей, 50 транзакций и 30 майнинг-инвестиций
                    </div>
                    
                    <hr class="border-gray-200">
                    
                    <form method="POST" class="w-full" onsubmit="return confirm('Вы уверены? Это действие нельзя отменить!')">
                        <input type="hidden" name="action" value="clear_fake_data">
                        <button type="submit" class="w-full bg-red-500 hover:bg-red-600 text-white py-3 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-trash mr-2"></i>Очистить тестовые данные
                        </button>
                    </form>
                    
                    <div class="text-center text-sm text-red-500">
                        ⚠️ Удалит всех пользователей кроме администраторов
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="mt-8 bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4">📊 Текущая статистика</h3>
            
            <?php
            $db = getDB();
            
            // Получаем статистику
            $stats = [
                'users' => $db->query("SELECT COUNT(*) FROM users WHERE role = 'user'")->fetchColumn(),
                'transactions' => $db->query("SELECT COUNT(*) FROM transactions")->fetchColumn(),
                'mining_investments' => $db->query("SELECT COUNT(*) FROM user_mining_investments")->fetchColumn(),
                'daily_profits' => $db->query("SELECT COUNT(*) FROM daily_mining_profits")->fetchColumn()
            ];
            ?>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600"><?php echo $stats['users']; ?></div>
                    <div class="text-sm text-gray-600">Пользователей</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600"><?php echo $stats['transactions']; ?></div>
                    <div class="text-sm text-gray-600">Транзакций</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600"><?php echo $stats['mining_investments']; ?></div>
                    <div class="text-sm text-gray-600">Майнинг-инвестиций</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-orange-600"><?php echo $stats['daily_profits']; ?></div>
                    <div class="text-sm text-gray-600">Ежедневных прибылей</div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/admin_footer.php'; ?>
