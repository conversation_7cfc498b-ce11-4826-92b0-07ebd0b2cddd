<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireLogin();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('investments.php');
}

if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    setFlashMessage('error', 'Invalid security token');
    redirect('investments.php');
}

$user_id = getCurrentUserId();
$investment_id = intval($_POST['investment_id'] ?? 0);
$amount = floatval($_POST['amount'] ?? 0);

if (!$investment_id || !$amount) {
    setFlashMessage('error', 'Invalid investment data');
    redirect('investments.php');
}

$result = processInvestment($user_id, $investment_id, $amount);

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($isAjax) {
    header('Content-Type: application/json');

    if ($result['success']) {
        // Get package details for animation
        require_once '../includes/mining_functions.php';
        $package = getMiningPackageById($investment_id);

        $response = [
            'success' => true,
            'message' => $result['message'],
            'animation_data' => [
                'amount' => number_format($amount, 2),
                'dailyProfit' => number_format($amount * ($package['daily_rate'] ?? 0.02), 2),
                'duration' => $package['duration_days'] ?? 365,
                'packageName' => $package['name'] ?? 'Eco Mining Package'
            ]
        ];
        echo json_encode($response);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
} else {
    // Traditional form submission
    if ($result['success']) {
        setFlashMessage('success', $result['message']);
        redirect('dashboard.php?investment_success=1&amount=' . $amount);
    } else {
        setFlashMessage('error', $result['message']);
        redirect('investment-detail.php?id=' . $investment_id);
    }
}
?>
