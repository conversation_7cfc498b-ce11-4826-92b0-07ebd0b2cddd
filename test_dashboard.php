<?php
/**
 * Тест дашборда AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест дашборда AstroGenix</h1>";

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

// Проверяем существование таблиц
echo "<h2>Проверка таблиц</h2>";

$tables_to_check = [
    'users' => 'Пользователи',
    'user_mining_investments' => 'Майнинг-инвестиции',
    'daily_mining_profits' => 'Ежедневные прибыли',
    'mining_packages' => 'Майнинг-пакеты',
    'transactions' => 'Транзакции'
];

$missing_tables = [];

foreach ($tables_to_check as $table => $description) {
    try {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "<p>✅ Таблица <strong>$table</strong> ($description) существует</p>";
        } else {
            echo "<p>❌ Таблица <strong>$table</strong> ($description) не найдена</p>";
            $missing_tables[] = $table;
        }
    } catch (Exception $e) {
        echo "<p>❌ Ошибка проверки таблицы $table: " . $e->getMessage() . "</p>";
        $missing_tables[] = $table;
    }
}

// Если есть недостающие таблицы, создаем их
if (!empty($missing_tables)) {
    echo "<h2>Создание недостающих таблиц</h2>";
    
    // Создаем базовые таблицы
    $sql_commands = [
        'users' => "
            CREATE TABLE IF NOT EXISTS `users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(50) NOT NULL,
                `email` varchar(100) NOT NULL,
                `password_hash` varchar(255) NOT NULL,
                `role` enum('user','admin') DEFAULT 'user',
                `referral_code` varchar(20) UNIQUE,
                `referred_by` int(11) DEFAULT NULL,
                `balance` decimal(10,2) DEFAULT 0.00,
                `total_invested` decimal(10,2) DEFAULT 0.00,
                `total_earned` decimal(10,2) DEFAULT 0.00,
                `verification_status` enum('unverified','pending','verified','rejected') DEFAULT 'unverified',
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `username` (`username`),
                UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'mining_packages' => "
            CREATE TABLE IF NOT EXISTS `mining_packages` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `description` text,
                `energy_type` enum('solar','wind','hydro','geothermal','hybrid','biomass') NOT NULL,
                `min_investment` decimal(10,2) NOT NULL,
                `max_investment` decimal(10,2) DEFAULT NULL,
                `daily_rate` decimal(6,4) NOT NULL,
                `duration_days` int(11) NOT NULL,
                `hash_power` varchar(50),
                `energy_consumption` decimal(8,2),
                `co2_saved` decimal(8,2),
                `location` varchar(100),
                `image_url` varchar(255),
                `features` json,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'user_mining_investments' => "
            CREATE TABLE IF NOT EXISTS `user_mining_investments` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `package_id` int(11) NOT NULL,
                `investment_amount` decimal(10,2) NOT NULL,
                `daily_rate` decimal(6,4) NOT NULL,
                `start_date` date NOT NULL,
                `end_date` date NOT NULL,
                `total_earned` decimal(10,2) DEFAULT 0.00,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `package_id` (`package_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'daily_mining_profits' => "
            CREATE TABLE IF NOT EXISTS `daily_mining_profits` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `investment_id` int(11) NOT NULL,
                `profit_amount` decimal(10,2) NOT NULL,
                `co2_saved` decimal(8,2) DEFAULT 0.00,
                `profit_date` date NOT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `investment_id` (`investment_id`),
                KEY `profit_date` (`profit_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'transactions' => "
            CREATE TABLE IF NOT EXISTS `transactions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `type` enum('deposit','withdrawal') NOT NULL,
                `amount` decimal(10,2) NOT NULL,
                `status` enum('pending','approved','rejected') DEFAULT 'pending',
                `screenshot_path` varchar(255) DEFAULT NULL,
                `wallet_address` varchar(255) DEFAULT NULL,
                `admin_notes` text,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        "
    ];
    
    foreach ($missing_tables as $table) {
        if (isset($sql_commands[$table])) {
            try {
                $db->exec($sql_commands[$table]);
                echo "<p>✅ Таблица <strong>$table</strong> создана</p>";
            } catch (Exception $e) {
                echo "<p>❌ Ошибка создания таблицы $table: " . $e->getMessage() . "</p>";
            }
        }
    }
}

// Тестируем функции дашборда
echo "<h2>Тест функций дашборда</h2>";

// Создаем тестового пользователя если его нет
try {
    $stmt = $db->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetch()['count'];
    
    if ($user_count == 0) {
        echo "<p>⚠️ Нет пользователей в системе. Создаем тестового пользователя...</p>";
        
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password_hash, role, referral_code) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'testuser',
            '<EMAIL>',
            password_hash('test123', PASSWORD_DEFAULT),
            'admin',
            'TEST123'
        ]);
        
        echo "<p>✅ Тестовый пользователь создан (testuser / test123)</p>";
    }
    
    // Получаем первого пользователя для тестирования
    $stmt = $db->query("SELECT id FROM users LIMIT 1");
    $test_user = $stmt->fetch();
    $test_user_id = $test_user['id'];
    
    echo "<p>📊 Тестируем с пользователем ID: $test_user_id</p>";
    
    // Тестируем getDashboardStats
    if (function_exists('getDashboardStats')) {
        $stats = getDashboardStats($test_user_id);
        echo "<p>✅ getDashboardStats() работает:</p>";
        echo "<ul>";
        echo "<li>Баланс: " . $stats['balance'] . "</li>";
        echo "<li>Всего инвестировано: " . $stats['total_invested'] . "</li>";
        echo "<li>Всего заработано: " . $stats['total_profit'] . "</li>";
        echo "<li>Активных инвестиций: " . $stats['active_investments'] . "</li>";
        echo "<li>CO₂ сэкономлено: " . $stats['total_co2_saved'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p>❌ Функция getDashboardStats() не найдена</p>";
    }
    
    // Тестируем getUserInvestments
    if (function_exists('getUserInvestments')) {
        $investments = getUserInvestments($test_user_id);
        echo "<p>✅ getUserInvestments() работает. Найдено инвестиций: " . count($investments) . "</p>";
    } else {
        echo "<p>❌ Функция getUserInvestments() не найдена</p>";
    }
    
    // Тестируем getUserTransactions
    if (function_exists('getUserTransactions')) {
        $transactions = getUserTransactions($test_user_id, 5);
        echo "<p>✅ getUserTransactions() работает. Найдено транзакций: " . count($transactions) . "</p>";
    } else {
        echo "<p>❌ Функция getUserTransactions() не найдена</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка тестирования функций: " . $e->getMessage() . "</p>";
}

// Итоговый результат
echo "<h2>🎯 Результат</h2>";

if (empty($missing_tables) && function_exists('getDashboardStats')) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 Дашборд готов к работе!</h3>";
    echo "<p><a href='pages/dashboard.php'>Перейти к дашборду</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Требуется дополнительная настройка</h3>";
    echo "<p>Импортируйте полную схему БД из файла database/astrogenix_eco_mining.sql</p>";
    echo "</div>";
}
?>
