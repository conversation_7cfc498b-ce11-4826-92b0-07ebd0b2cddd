# 🔧 Исправление ошибок AstroGenix

## ✅ Исправленные проблемы

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> "Failed to open stream: No such file or directory"
**Проблема:** Неправильные пути к файлам в includes/
**Решение:** ✅ Исправлено - используются абсолютные пути с `__DIR__`

### 2. Ошибка подключения к базе данных
**Проблема:** База данных не создана или неправильное имя
**Решение:** ✅ Создан скрипт `setup_database.php` для автоматической настройки

## 🚀 Быстрое исправление для OSPanel

### Шаг 1: Проверьте подключение
```
http://genix/test_connection.php
```
Этот файл покажет все проблемы и их решения.

### Шаг 2: Настройте базу данных
```
http://genix/setup_database.php
```
Автоматически создаст базу данных с правильным именем.

### Шаг 3: Импортируйте полную схему
1. Откройте phpMyAdmin: `http://localhost/openserver/phpmyadmin/`
2. Выберите базу `astrogenix_eco_mining`
3. Импортируйте файл `database/astrogenix_eco_mining.sql`

### Шаг 4: Проверьте работу
```
http://genix/
```
Главная страница должна загружаться без ошибок.

## 📋 Контрольный список

- ✅ OSPanel запущен (Apache + MySQL)
- ✅ Файлы скопированы в `C:\OSPanel\domains\genix\`
- ✅ База данных `astrogenix_eco_mining` создана
- ✅ Схема БД импортирована
- ✅ Главная страница загружается
- ✅ Нет ошибок в консоли браузера

## 🆘 Если проблемы остались

### Ошибка 500 (Internal Server Error)
1. Проверьте логи Apache: `C:\OSPanel\userdata\logs\Apache_error.log`
2. Включите отображение ошибок PHP в OSPanel
3. Убедитесь, что PHP версии 7.4+

### Белая страница
1. Откройте консоль браузера (F12)
2. Проверьте вкладку Network на ошибки
3. Убедитесь, что все CSS/JS файлы загружаются

### Ошибки JavaScript
1. Проверьте подключение к интернету (для CDN)
2. Убедитесь, что файл `assets/js/eco-animations.js` существует
3. Проверьте консоль браузера на ошибки

## 📞 Техническая поддержка

Если ничего не помогает:
1. Запустите `test_connection.php` и сохраните результат
2. Проверьте логи OSPanel
3. Убедитесь, что все файлы проекта на месте

**Система должна работать из коробки после выполнения этих шагов!** 🎉
