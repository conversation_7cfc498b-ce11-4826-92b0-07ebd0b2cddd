<?php
/**
 * Финальный тест всех исправлений базы данных AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🎯 Финальный тест исправлений базы данных AstroGenix</h1>";

$all_tests_passed = true;
$failed_tests = [];

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. Тест исправленных SQL-запросов</h2>";

// Тест 1: Админ-панель - общая сумма инвестиций
echo "<h3>1.1 Админ-панель - сумма инвестиций</h3>";
try {
    $stmt = $db->prepare("SELECT SUM(amount) as total FROM user_mining_investments WHERE is_active = 1");
    $stmt->execute();
    $total_invested = $stmt->fetch()['total'] ?? 0;
    echo "<p>✅ Запрос суммы инвестиций работает: " . formatCurrency($total_invested) . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка запроса суммы инвестиций: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Сумма инвестиций в админ-панели";
}

// Тест 2: Админ-панель - недавние активности
echo "<h3>1.2 Админ-панель - недавние активности</h3>";
try {
    $stmt = $db->prepare("
        SELECT 'mining_investment' as type, umi.created_at, u.username, mp.name as title, umi.amount as amount
        FROM user_mining_investments umi
        JOIN users u ON umi.user_id = u.id
        JOIN mining_packages mp ON umi.package_id = mp.id
        ORDER BY umi.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_investments = $stmt->fetchAll();
    echo "<p>✅ Запрос недавних активностей работает: " . count($recent_investments) . " записей</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка запроса недавних активностей: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Недавние активности в админ-панели";
}

// Тест 3: getDashboardStats
echo "<h3>1.3 Функция getDashboardStats</h3>";
try {
    // Получаем первого пользователя
    $stmt = $db->query("SELECT id FROM users LIMIT 1");
    $user = $stmt->fetch();
    
    if ($user) {
        $stats = getDashboardStats($user['id']);
        echo "<p>✅ getDashboardStats работает:</p>";
        echo "<ul>";
        echo "<li>Баланс: " . $stats['balance'] . "</li>";
        echo "<li>Инвестировано: " . $stats['total_invested'] . "</li>";
        echo "<li>Заработано: " . $stats['total_profit'] . "</li>";
        echo "<li>Активных инвестиций: " . $stats['active_investments'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p>⚠️ Нет пользователей для тестирования getDashboardStats</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getDashboardStats: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getDashboardStats";
}

// Тест 4: getUserTransactions
echo "<h3>1.4 Функция getUserTransactions</h3>";
try {
    if (isset($user)) {
        $transactions = getUserTransactions($user['id'], 5);
        echo "<p>✅ getUserTransactions работает: " . count($transactions) . " транзакций</p>";
    } else {
        echo "<p>⚠️ Нет пользователей для тестирования getUserTransactions</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getUserTransactions: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getUserTransactions";
}

echo "<h2>2. Тест функций совместимости</h2>";

// Тест 5: getAllInvestments (совместимость)
echo "<h3>2.1 Функция getAllInvestments (совместимость)</h3>";
try {
    $investments = getAllInvestments(true);
    echo "<p>✅ getAllInvestments (совместимость) работает: " . count($investments) . " пакетов</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка getAllInvestments: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getAllInvestments (совместимость)";
}

// Тест 6: getBlogPosts (совместимость)
echo "<h3>2.2 Функция getBlogPosts (совместимость)</h3>";
try {
    $blog_posts = getBlogPosts(true, 5);
    echo "<p>✅ getBlogPosts (совместимость) работает: " . count($blog_posts) . " новостей</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка getBlogPosts: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getBlogPosts (совместимость)";
}

echo "<h2>3. Тест функций рейтингов (без дублирования)</h2>";

// Тест 7: getMiningRankings
echo "<h3>3.1 Функция getMiningRankings</h3>";
try {
    if (function_exists('getMiningRankings')) {
        $mining_rankings = getMiningRankings(10);
        echo "<p>✅ getMiningRankings работает: " . count($mining_rankings) . " майнеров в рейтинге</p>";
    } else {
        echo "<p>❌ getMiningRankings не найдена</p>";
        $all_tests_passed = false;
        $failed_tests[] = "Функция getMiningRankings не найдена";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getMiningRankings: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getMiningRankings";
}

// Тест 8: getReferralRankings
echo "<h3>3.2 Функция getReferralRankings</h3>";
try {
    if (function_exists('getReferralRankings')) {
        $referral_rankings = getReferralRankings(10);
        echo "<p>✅ getReferralRankings работает: " . count($referral_rankings) . " рефереров в рейтинге</p>";
    } else {
        echo "<p>❌ getReferralRankings не найдена</p>";
        $all_tests_passed = false;
        $failed_tests[] = "Функция getReferralRankings не найдена";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getReferralRankings: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getReferralRankings";
}

echo "<h2>4. Тест реферальных функций</h2>";

// Тест 9: getReferralStats
echo "<h3>4.1 Функция getReferralStats</h3>";
try {
    if (isset($user)) {
        $referral_stats = getReferralStats($user['id']);
        echo "<p>✅ getReferralStats работает:</p>";
        echo "<ul>";
        echo "<li>Всего рефералов: " . $referral_stats['total_referrals'] . "</li>";
        echo "<li>Активных рефералов: " . $referral_stats['active_referrals'] . "</li>";
        echo "<li>Всего заработано: " . formatCurrency($referral_stats['total_earnings']) . "</li>";
        echo "</ul>";
    } else {
        echo "<p>⚠️ Нет пользователей для тестирования getReferralStats</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getReferralStats: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getReferralStats";
}

// Тест 10: getMyReferrals
echo "<h3>4.2 Функция getMyReferrals</h3>";
try {
    if (isset($user)) {
        $my_referrals = getMyReferrals($user['id']);
        echo "<p>✅ getMyReferrals работает: " . count($my_referrals) . " рефералов</p>";
    } else {
        echo "<p>⚠️ Нет пользователей для тестирования getMyReferrals</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getMyReferrals: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
    $failed_tests[] = "Функция getMyReferrals";
}

echo "<h2>5. Тест доступности страниц</h2>";

$pages_to_test = [
    'pages/rankings.php' => 'Страница рейтингов',
    'pages/referral.php' => 'Реферальная программа',
    'pages/investments.php' => 'Страница инвестиций',
    'pages/dashboard.php' => 'Дашборд',
    'admin/index.php' => 'Админ-панель'
];

foreach ($pages_to_test as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>❌ $description ($page) не найден</p>";
        $all_tests_passed = false;
        $failed_tests[] = "Страница: $description";
    }
}

echo "<h2>6. Проверка структуры таблиц</h2>";

$required_tables = [
    'users' => 'Пользователи',
    'user_mining_investments' => 'Майнинг-инвестиции',
    'daily_mining_profits' => 'Ежедневные прибыли',
    'mining_packages' => 'Майнинг-пакеты',
    'transactions' => 'Транзакции',
    'referrals' => 'Рефералы',
    'referral_earnings' => 'Доходы рефералов',
    'eco_mining_news' => 'Экологические новости'
];

foreach ($required_tables as $table => $description) {
    try {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "<p>✅ Таблица $table ($description) существует</p>";
        } else {
            echo "<p>⚠️ Таблица $table ($description) не найдена</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Ошибка проверки таблицы $table: " . $e->getMessage() . "</p>";
    }
}

// Итоговый результат
echo "<h2>🎯 Итоговый результат</h2>";

if ($all_tests_passed && empty($failed_tests)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 ВСЕ ИСПРАВЛЕНИЯ УСПЕШНО ПРИМЕНЕНЫ!</h3>";
    echo "<p><strong>✅ Исправленные проблемы:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Ошибки схемы БД (investment_amount → amount)</li>";
    echo "<li>✅ Отсутствующая таблица blog_posts (перенаправление на eco_mining_news)</li>";
    echo "<li>✅ Старая таблица investments (совместимость с mining_packages)</li>";
    echo "<li>✅ Система рейтингов майнеров и рефереров</li>";
    echo "<li>✅ Реферальная программа для пользователей</li>";
    echo "<li>✅ Дублирование функций рейтингов устранено</li>";
    echo "<li>✅ Страница инвестиций исправлена (совместимость полей)</li>";
    echo "</ul>";
    echo "<p><strong>🚀 Доступные функции:</strong></p>";
    echo "<ul>";
    echo "<li><a href='pages/rankings.php' target='_blank'>🏆 Рейтинги платформы</a></li>";
    echo "<li><a href='pages/referral.php' target='_blank'>🤝 Реферальная программа</a></li>";
    echo "<li><a href='pages/investments.php' target='_blank'>💰 Страница инвестиций</a></li>";
    echo "<li><a href='pages/dashboard.php' target='_blank'>📊 Дашборд пользователя</a></li>";
    echo "<li><a href='admin/index.php' target='_blank'>🔧 Админ-панель</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p><strong>Неудачные тесты:</strong></p>";
    echo "<ul>";
    foreach ($failed_tests as $test) {
        echo "<li>$test</li>";
    }
    echo "</ul>";
    echo "<p><strong>Рекомендации:</strong></p>";
    echo "<ul>";
    echo "<li>Импортируйте полную схему БД из database/schema.sql</li>";
    echo "<li>Проверьте права доступа к файлам</li>";
    echo "<li>Убедитесь, что все таблицы созданы</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . " | AstroGenix Eco Mining Platform v2.0</small></p>";
?>
