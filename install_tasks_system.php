<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Simple password protection for installation
$install_password = 'admin123'; // Change this password

if (!isset($_POST['install_password'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Install Tasks System - AstroGenix</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 500px; margin: 100px auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #10b981; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #059669; }
            .warning { background: #fef3cd; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <h1>🌱 Install AstroGenix Tasks System</h1>
        <div class="warning">
            <strong>⚠️ Warning:</strong> This will create new database tables for the tasks system.
        </div>
        <form method="POST">
            <div class="form-group">
                <label>Installation Password:</label>
                <input type="password" name="install_password" required>
                <small>Default password: admin123</small>
            </div>
            <button type="submit">Install Tasks System</button>
        </form>
    </body>
    </html>
    <?php
    exit;
}

if ($_POST['install_password'] !== $install_password) {
    die('<h1>❌ Access Denied</h1><p>Incorrect installation password.</p><a href="install_tasks_system.php">Try again</a>');
}

echo "<h1>Installing Tasks System...</h1>";

try {
    // Get database connection
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }

    // Read and execute the tasks schema
    $schema_file = __DIR__ . '/database/tasks_schema.sql';

    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: $schema_file");
    }

    $sql = file_get_contents($schema_file);

    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "<p>Executing: " . substr($statement, 0, 50) . "...</p>";
            try {
                $pdo->exec($statement);
                echo "<span style='color: green;'>✓ Success</span><br>";
            } catch (PDOException $e) {
                // Check if it's just a "table already exists" error
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "<span style='color: orange;'>⚠ Table already exists, skipping</span><br>";
                } else {
                    throw $e; // Re-throw if it's a different error
                }
            }
        }
    }

    $pdo->commit();

    echo "<h2 style='color: green;'>✅ Tasks system installed successfully!</h2>";
    echo "<p>The following tables have been created or verified:</p>";
    echo "<ul>";
    echo "<li>tasks - Stores available tasks and missions</li>";
    echo "<li>user_task_progress - Tracks user progress on tasks</li>";
    echo "<li>task_completions - Historical log of completed tasks</li>";
    echo "</ul>";

    echo "<p>Default tasks have been inserted:</p>";
    echo "<ul>";
    echo "<li>Investment tasks (First Investment, Big Investor, etc.)</li>";
    echo "<li>Referral tasks (First Referral, Referral Master, etc.)</li>";
    echo "<li>Engagement tasks (Daily login streaks)</li>";
    echo "<li>Environmental tasks (CO2 savings milestones)</li>";
    echo "</ul>";

    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Visit <a href='pages/tasks.php'>Tasks Page</a> to see the user interface</li>";
    echo "<li>Visit <a href='admin/tasks.php'>Admin Tasks Panel</a> to manage tasks</li>";
    echo "<li>Test the system by making investments or inviting referrals</li>";
    echo "</ol>";

} catch (Exception $e) {
    // Check if transaction is still active before rolling back
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "<h2 style='color: red;'>❌ Installation failed!</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";

    // Show more detailed error information
    echo "<details>";
    echo "<summary>Click for detailed error information</summary>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</details>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
p, li {
    margin-bottom: 10px;
}
ul, ol {
    margin-left: 20px;
}
a {
    color: #10b981;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
