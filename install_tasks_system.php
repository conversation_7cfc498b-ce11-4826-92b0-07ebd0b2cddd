<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Check if user is admin (basic security)
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Installing Tasks System...</h1>";

try {
    // Read and execute the tasks schema
    $schema_file = 'database/tasks_schema.sql';
    
    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: $schema_file");
    }
    
    $sql = file_get_contents($schema_file);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "<p>Executing: " . substr($statement, 0, 50) . "...</p>";
            $pdo->exec($statement);
        }
    }
    
    $pdo->commit();
    
    echo "<h2 style='color: green;'>✅ Tasks system installed successfully!</h2>";
    echo "<p>The following tables have been created:</p>";
    echo "<ul>";
    echo "<li>tasks - Stores available tasks and missions</li>";
    echo "<li>user_task_progress - Tracks user progress on tasks</li>";
    echo "<li>task_completions - Historical log of completed tasks</li>";
    echo "</ul>";
    
    echo "<p>Default tasks have been inserted:</p>";
    echo "<ul>";
    echo "<li>Investment tasks (First Investment, Big Investor, etc.)</li>";
    echo "<li>Referral tasks (First Referral, Referral Master, etc.)</li>";
    echo "<li>Engagement tasks (Daily login streaks)</li>";
    echo "<li>Environmental tasks (CO2 savings milestones)</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Visit <a href='pages/tasks.php'>Tasks Page</a> to see the user interface</li>";
    echo "<li>Visit <a href='admin/tasks.php'>Admin Tasks Panel</a> to manage tasks</li>";
    echo "<li>Test the system by making investments or inviting referrals</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h2 style='color: red;'>❌ Installation failed!</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
p, li {
    margin-bottom: 10px;
}
ul, ol {
    margin-left: 20px;
}
a {
    color: #10b981;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
