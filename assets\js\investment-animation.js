/**
 * Investment Success Animation
 * Eco-mining themed animation for successful investments
 */

class InvestmentAnimation {
    constructor() {
        this.overlay = null;
        this.soundEnabled = true;
        this.init();
    }

    init() {
        this.createOverlay();
        this.loadSounds();
    }

    createOverlay() {
        // Create overlay HTML
        const overlayHTML = `
            <div id="investment-success-overlay" class="investment-success-overlay">
                <div class="investment-success-content">
                    <div class="success-title">🌱 Mining Farm Activated!</div>
                    
                    <div class="eco-animation-container">
                        <!-- Floating leaves -->
                        <div class="leaf"></div>
                        <div class="leaf"></div>
                        <div class="leaf"></div>
                        <div class="leaf"></div>
                        <div class="leaf"></div>
                        
                        <!-- Energy pulse -->
                        <div class="energy-pulse"></div>
                        
                        <!-- CO2 reduction indicators -->
                        <div class="co2-indicator">-15kg CO₂</div>
                        <div class="co2-indicator">+Clean Energy</div>
                        <div class="co2-indicator">🌍 Eco Mining</div>
                        
                        <!-- Mining farm -->
                        <div class="mining-farm">
                            <div class="mining-rig"></div>
                            <div class="mining-rig"></div>
                            <div class="mining-rig"></div>
                            <div class="mining-rig"></div>
                            <div class="mining-rig"></div>
                        </div>
                    </div>
                    
                    <div class="success-details">
                        <div id="investment-amount">Investment: $0</div>
                        <div id="daily-profit">Daily Profit: $0</div>
                        <div id="mining-duration">Duration: 0 days</div>
                    </div>
                    
                    <button class="close-animation-btn" onclick="investmentAnimation.hide()">
                        Continue Mining 🚀
                    </button>
                </div>
            </div>
        `;

        // Add to body
        document.body.insertAdjacentHTML('beforeend', overlayHTML);
        this.overlay = document.getElementById('investment-success-overlay');
    }

    loadSounds() {
        // Create audio elements for sound effects
        this.sounds = {
            success: this.createAudio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            activation: this.createAudio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
        };
    }

    createAudio(dataUrl) {
        try {
            const audio = new Audio(dataUrl);
            audio.volume = 0.3;
            return audio;
        } catch (e) {
            console.log('Audio not supported');
            return null;
        }
    }

    show(investmentData = {}) {
        // Update investment details
        this.updateDetails(investmentData);
        
        // Play success sound
        this.playSound('success');
        
        // Show overlay
        this.overlay.classList.add('active');
        
        // Auto-hide after 8 seconds
        setTimeout(() => {
            this.hide();
        }, 8000);
        
        // Play activation sound after 1 second
        setTimeout(() => {
            this.playSound('activation');
        }, 1000);
    }

    hide() {
        this.overlay.classList.remove('active');
    }

    updateDetails(data) {
        const amountEl = document.getElementById('investment-amount');
        const profitEl = document.getElementById('daily-profit');
        const durationEl = document.getElementById('mining-duration');

        if (amountEl) amountEl.textContent = `Investment: $${data.amount || 0}`;
        if (profitEl) profitEl.textContent = `Daily Profit: $${data.dailyProfit || 0}`;
        if (durationEl) durationEl.textContent = `Duration: ${data.duration || 0} days`;
    }

    playSound(type) {
        if (this.soundEnabled && this.sounds[type]) {
            try {
                this.sounds[type].currentTime = 0;
                this.sounds[type].play().catch(e => {
                    console.log('Sound play failed:', e);
                });
            } catch (e) {
                console.log('Sound error:', e);
            }
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        return this.soundEnabled;
    }
}

// Initialize animation when DOM is loaded
let investmentAnimation;
document.addEventListener('DOMContentLoaded', function() {
    investmentAnimation = new InvestmentAnimation();
});

// Global function to trigger animation
function showInvestmentSuccess(investmentData) {
    if (investmentAnimation) {
        investmentAnimation.show(investmentData);
    }
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InvestmentAnimation;
}
