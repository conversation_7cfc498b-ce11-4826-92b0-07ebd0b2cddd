<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Исправление системы депозитов - AstroGenix</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔧 Исправление системы депозитов</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $fixes = [];
            $allFixed = true;
            
            try {
                $db = getDB();
                
                // Fix 1: Check and create uploads directory
                $uploadDir = 'uploads/transactions/';
                if (!file_exists($uploadDir)) {
                    if (mkdir($uploadDir, 0755, true)) {
                        $fixes[] = ['name' => 'Создание директории uploads/transactions/', 'status' => 'fixed', 'message' => 'Директория создана успешно'];
                    } else {
                        $fixes[] = ['name' => 'Создание директории uploads/transactions/', 'status' => 'error', 'message' => 'Не удалось создать директорию'];
                        $allFixed = false;
                    }
                } else {
                    $fixes[] = ['name' => 'Проверка директории uploads/transactions/', 'status' => 'ok', 'message' => 'Директория существует'];
                }
                
                // Fix 2: Check directory permissions
                if (is_writable($uploadDir)) {
                    $fixes[] = ['name' => 'Права доступа к директории', 'status' => 'ok', 'message' => 'Директория доступна для записи'];
                } else {
                    if (chmod($uploadDir, 0755)) {
                        $fixes[] = ['name' => 'Права доступа к директории', 'status' => 'fixed', 'message' => 'Права доступа исправлены'];
                    } else {
                        $fixes[] = ['name' => 'Права доступа к директории', 'status' => 'error', 'message' => 'Не удалось установить права доступа'];
                        $allFixed = false;
                    }
                }
                
                // Fix 3: Check PHP upload settings
                $uploadMaxFilesize = ini_get('upload_max_filesize');
                $postMaxSize = ini_get('post_max_size');
                $maxFileUploads = ini_get('max_file_uploads');
                
                $fixes[] = ['name' => 'Настройки PHP для загрузки файлов', 'status' => 'info', 'message' => "upload_max_filesize: $uploadMaxFilesize, post_max_size: $postMaxSize, max_file_uploads: $maxFileUploads"];
                
                // Fix 4: Test file upload function
                if (function_exists('uploadTransactionScreenshot')) {
                    $fixes[] = ['name' => 'Функция uploadTransactionScreenshot', 'status' => 'ok', 'message' => 'Функция существует'];
                } else {
                    $fixes[] = ['name' => 'Функция uploadTransactionScreenshot', 'status' => 'error', 'message' => 'Функция не найдена'];
                    $allFixed = false;
                }
                
                // Fix 5: Test processDepositRequest function
                if (function_exists('processDepositRequest')) {
                    $fixes[] = ['name' => 'Функция processDepositRequest', 'status' => 'ok', 'message' => 'Функция существует'];
                } else {
                    $fixes[] = ['name' => 'Функция processDepositRequest', 'status' => 'error', 'message' => 'Функция не найдена'];
                    $allFixed = false;
                }
                
                // Fix 6: Check transactions table
                try {
                    $stmt = $db->query("DESCRIBE transactions");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    if (in_array('screenshot_path', $columns)) {
                        $fixes[] = ['name' => 'Поле screenshot_path в таблице transactions', 'status' => 'ok', 'message' => 'Поле существует'];
                    } else {
                        // Add the column
                        $db->exec("ALTER TABLE transactions ADD COLUMN screenshot_path VARCHAR(255) DEFAULT NULL");
                        $fixes[] = ['name' => 'Поле screenshot_path в таблице transactions', 'status' => 'fixed', 'message' => 'Поле добавлено'];
                    }
                } catch (Exception $e) {
                    $fixes[] = ['name' => 'Поле screenshot_path в таблице transactions', 'status' => 'error', 'message' => 'Ошибка: ' . $e->getMessage()];
                    $allFixed = false;
                }
                
                // Fix 7: Create test upload to verify everything works
                if ($allFixed) {
                    // Create a test image
                    $testImageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
                    $testFilePath = 'test_upload_fix.png';
                    file_put_contents($testFilePath, $testImageData);
                    
                    $testFile = [
                        'name' => 'test_upload_fix.png',
                        'type' => 'image/png',
                        'size' => strlen($testImageData),
                        'tmp_name' => $testFilePath,
                        'error' => 0
                    ];
                    
                    $uploadResult = uploadTransactionScreenshot($testFile);
                    
                    if ($uploadResult['success']) {
                        $fixes[] = ['name' => 'Тестовая загрузка файла', 'status' => 'ok', 'message' => 'Файл успешно загружен: ' . $uploadResult['filepath']];
                        
                        // Clean up test files
                        if (file_exists($testFilePath)) unlink($testFilePath);
                        if (file_exists($uploadResult['filepath'])) unlink($uploadResult['filepath']);
                    } else {
                        $fixes[] = ['name' => 'Тестовая загрузка файла', 'status' => 'error', 'message' => 'Ошибка загрузки: ' . $uploadResult['message']];
                        $allFixed = false;
                    }
                }
                
            } catch (Exception $e) {
                $fixes[] = ['name' => 'Общая ошибка', 'status' => 'error', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allFixed = false;
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($fixes as $fix) {
                $bgColor = $fix['status'] === 'ok' ? 'bg-green-50 border-green-200' : 
                          ($fix['status'] === 'fixed' ? 'bg-blue-50 border-blue-200' :
                          ($fix['status'] === 'info' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200'));
                $textColor = $fix['status'] === 'ok' ? 'text-green-800' : 
                            ($fix['status'] === 'fixed' ? 'text-blue-800' :
                            ($fix['status'] === 'info' ? 'text-yellow-800' : 'text-red-800'));
                $icon = $fix['status'] === 'ok' ? '✅' : 
                       ($fix['status'] === 'fixed' ? '🔧' :
                       ($fix['status'] === 'info' ? 'ℹ️' : '❌'));
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $fix['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $fix['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $fix['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allFixed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allFixed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allFixed ? '🎉 Система депозитов исправлена!' : '⚠️ Обнаружены проблемы';
            echo '</h2>';
            echo '<p class="' . ($allFixed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allFixed) {
                echo 'Все компоненты системы депозитов работают корректно. Загрузка файлов должна работать.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы для корректной работы системы депозитов.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Diagnostic Information -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🔍 Диагностическая информация</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">Настройки PHP</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>upload_max_filesize: <?php echo ini_get('upload_max_filesize'); ?></li>
                            <li>post_max_size: <?php echo ini_get('post_max_size'); ?></li>
                            <li>max_file_uploads: <?php echo ini_get('max_file_uploads'); ?></li>
                            <li>file_uploads: <?php echo ini_get('file_uploads') ? 'Включено' : 'Отключено'; ?></li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">Директории</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>uploads/: <?php echo file_exists('uploads/') ? '✅ Существует' : '❌ Не найдена'; ?></li>
                            <li>uploads/transactions/: <?php echo file_exists('uploads/transactions/') ? '✅ Существует' : '❌ Не найдена'; ?></li>
                            <li>Права записи: <?php echo is_writable('uploads/transactions/') ? '✅ Доступна' : '❌ Недоступна'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Test Links -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🧪 Тестирование</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="pages/deposit.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Тест депозита
                    </a>
                    <a href="admin/deposits.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-eye mr-2"></i>Админ депозиты
                    </a>
                    <a href="debug_upload.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-bug mr-2"></i>Отладка загрузки
                    </a>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">📋 Инструкции по исправлению</h3>
                <div class="text-sm text-blue-800 space-y-2">
                    <p><strong>1. Проверьте права доступа:</strong></p>
                    <code class="bg-blue-100 px-2 py-1 rounded">chmod 755 uploads/transactions/</code>
                    
                    <p><strong>2. Проверьте настройки PHP в php.ini:</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>file_uploads = On</li>
                        <li>upload_max_filesize = 10M</li>
                        <li>post_max_size = 10M</li>
                        <li>max_file_uploads = 20</li>
                    </ul>
                    
                    <p><strong>3. Перезапустите веб-сервер после изменения php.ini</strong></p>
                    
                    <p><strong>4. Проверьте логи ошибок PHP для дополнительной информации</strong></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
