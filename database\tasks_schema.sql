-- Tasks/Missions System Schema for AstroGenix
-- This file contains the database structure for the tasks and missions system

-- Tasks table - defines available tasks/missions
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    task_type ENUM('investment', 'referral', 'login_streak', 'milestone', 'special') NOT NULL,
    
    -- Task requirements (JSON format)
    requirements JSON NOT NULL COMMENT 'Task requirements like amount, count, etc.',
    
    -- Rewards
    reward_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    reward_type ENUM('usdt', 'bonus_points', 'multiplier') DEFAULT 'usdt',
    
    -- Task settings
    is_active BOOLEAN DEFAULT TRUE,
    is_repeatable BOOLEAN DEFAULT FALSE,
    max_completions INT DEFAULT 1 COMMENT 'Max times a user can complete this task',
    
    -- Availability
    start_date DATETIME NULL,
    end_date DATETIME NULL,
    
    -- Display settings
    icon VARCHAR(100) DEFAULT 'fas fa-tasks',
    difficulty ENUM('easy', 'medium', 'hard', 'expert') DEFAULT 'easy',
    category VARCHAR(50) DEFAULT 'general',
    sort_order INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_task_type (task_type),
    INDEX idx_active (is_active),
    INDEX idx_category (category),
    INDEX idx_dates (start_date, end_date)
);

-- User task progress table - tracks user progress on tasks
CREATE TABLE IF NOT EXISTS user_task_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    
    -- Progress tracking
    current_progress INT DEFAULT 0,
    required_progress INT NOT NULL,
    progress_data JSON NULL COMMENT 'Additional progress data',
    
    -- Status
    status ENUM('not_started', 'in_progress', 'completed', 'claimed') DEFAULT 'not_started',
    
    -- Completion tracking
    completed_at TIMESTAMP NULL,
    claimed_at TIMESTAMP NULL,
    reward_claimed DECIMAL(15,2) DEFAULT 0.00,
    
    -- Metadata
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_task (user_id, task_id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_task_status (task_id, status)
);

-- Task completions log - historical record of task completions
CREATE TABLE IF NOT EXISTS task_completions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    
    -- Completion details
    completion_data JSON NULL COMMENT 'Data about how task was completed',
    reward_amount DECIMAL(15,2) NOT NULL,
    reward_type VARCHAR(50) NOT NULL,
    
    -- Timestamps
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    
    INDEX idx_user_completed (user_id, completed_at),
    INDEX idx_task_completed (task_id, completed_at)
);

-- Insert default tasks
INSERT IGNORE INTO tasks (title, description, task_type, requirements, reward_amount, icon, difficulty, category) VALUES
-- Investment tasks
('First Investment', 'Make your first eco-mining investment of any amount', 'investment', '{"min_amount": 1, "count": 1}', 25.00, 'fas fa-seedling', 'easy', 'investment'),
('Big Investor', 'Invest $1000 or more in a single transaction', 'investment', '{"min_amount": 1000, "count": 1}', 100.00, 'fas fa-gem', 'medium', 'investment'),
('Eco Enthusiast', 'Invest in 3 different mining packages', 'investment', '{"different_packages": 3}', 75.00, 'fas fa-leaf', 'medium', 'investment'),
('Mining Mogul', 'Reach $5000 total invested', 'milestone', '{"total_invested": 5000}', 250.00, 'fas fa-crown', 'hard', 'investment'),

-- Referral tasks
('First Referral', 'Invite your first friend to join AstroGenix', 'referral', '{"count": 1}', 50.00, 'fas fa-user-plus', 'easy', 'referral'),
('Referral Master', 'Invite 5 friends who each make an investment', 'referral', '{"active_referrals": 5}', 200.00, 'fas fa-users', 'medium', 'referral'),
('Community Builder', 'Invite 10 active referrals', 'referral', '{"active_referrals": 10}', 500.00, 'fas fa-network-wired', 'hard', 'referral'),

-- Login streak tasks
('Daily Miner', 'Login for 7 consecutive days', 'login_streak', '{"days": 7}', 30.00, 'fas fa-calendar-check', 'easy', 'engagement'),
('Dedicated Miner', 'Login for 30 consecutive days', 'login_streak', '{"days": 30}', 150.00, 'fas fa-fire', 'medium', 'engagement'),

-- Special milestone tasks
('Green Pioneer', 'Save 100kg of CO2 through eco mining', 'milestone', '{"co2_saved": 100}', 80.00, 'fas fa-globe-americas', 'medium', 'environmental'),
('Sustainability Champion', 'Save 1000kg of CO2 through eco mining', 'milestone', '{"co2_saved": 1000}', 400.00, 'fas fa-award', 'hard', 'environmental');
