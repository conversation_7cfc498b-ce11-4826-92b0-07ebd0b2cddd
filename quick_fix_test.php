<?php
/**
 * Быстрый тест всех исправлений AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 Быстрый тест исправлений AstroGenix</h1>";

$all_good = true;
$issues = [];

// Тест 1: Подключение файлов
echo "<h2>1. Подключение файлов</h2>";
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения: " . $e->getMessage() . "</p>";
    $all_good = false;
    $issues[] = "Проблема с подключением файлов";
}

// Тест 2: Подключение к БД
echo "<h2>2. Подключение к БД</h2>";
try {
    $db = getDB();
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    echo "<p>✅ БД подключена: <strong>" . $result['db_name'] . "</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка БД: " . $e->getMessage() . "</p>";
    $all_good = false;
    $issues[] = "Проблема с подключением к БД";
}

// Тест 3: Проверка ключевых функций
echo "<h2>3. Ключевые функции</h2>";
$critical_functions = [
    'getDashboardStats',
    'getUserInvestments', 
    'getUserTransactions',
    'createMiningInvestment',
    'getAllMiningPackages'
];

foreach ($critical_functions as $func) {
    if (function_exists($func)) {
        echo "<p>✅ $func() доступна</p>";
    } else {
        echo "<p>❌ $func() НЕ НАЙДЕНА</p>";
        $all_good = false;
        $issues[] = "Функция $func() недоступна";
    }
}

// Тест 4: Проверка таблиц (если БД доступна)
if (isset($db)) {
    echo "<h2>4. Проверка таблиц</h2>";
    $required_tables = [
        'users',
        'user_mining_investments', 
        'daily_mining_profits',
        'mining_packages',
        'transactions'
    ];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '$table'");
            if ($stmt->fetch()) {
                echo "<p>✅ Таблица $table существует</p>";
            } else {
                echo "<p>⚠️ Таблица $table не найдена</p>";
                $issues[] = "Таблица $table отсутствует";
            }
        } catch (Exception $e) {
            echo "<p>❌ Ошибка проверки $table: " . $e->getMessage() . "</p>";
            $issues[] = "Ошибка проверки таблицы $table";
        }
    }
}

// Тест 5: Тест функций с данными (если есть пользователи)
if (isset($db)) {
    echo "<h2>5. Тест функций с данными</h2>";
    
    try {
        // Проверяем наличие пользователей
        $stmt = $db->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch()['count'];
        
        if ($user_count > 0) {
            // Берем первого пользователя
            $stmt = $db->query("SELECT id FROM users LIMIT 1");
            $user = $stmt->fetch();
            $user_id = $user['id'];
            
            // Тест getDashboardStats
            try {
                $stats = getDashboardStats($user_id);
                echo "<p>✅ getDashboardStats() работает (баланс: {$stats['balance']})</p>";
            } catch (Exception $e) {
                echo "<p>❌ getDashboardStats() ошибка: " . $e->getMessage() . "</p>";
                $all_good = false;
                $issues[] = "getDashboardStats() не работает";
            }
            
            // Тест getUserInvestments
            try {
                $investments = getUserInvestments($user_id);
                echo "<p>✅ getUserInvestments() работает (" . count($investments) . " инвестиций)</p>";
            } catch (Exception $e) {
                echo "<p>❌ getUserInvestments() ошибка: " . $e->getMessage() . "</p>";
                $all_good = false;
                $issues[] = "getUserInvestments() не работает";
            }
            
            // Тест getUserTransactions
            try {
                $transactions = getUserTransactions($user_id, 5);
                echo "<p>✅ getUserTransactions() работает (" . count($transactions) . " транзакций)</p>";
            } catch (Exception $e) {
                echo "<p>❌ getUserTransactions() ошибка: " . $e->getMessage() . "</p>";
                $all_good = false;
                $issues[] = "getUserTransactions() не работает";
            }
            
        } else {
            echo "<p>⚠️ Нет пользователей для тестирования функций</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка тестирования с данными: " . $e->getMessage() . "</p>";
        $issues[] = "Ошибка тестирования с данными";
    }
}

// Тест 6: Проверка страниц
echo "<h2>6. Проверка доступности страниц</h2>";
$pages_to_check = [
    'index.php' => 'Главная страница',
    'pages/dashboard.php' => 'Дашборд',
    'pages/mining-packages.php' => 'Майнинг-пакеты',
    'pages/register.php' => 'Регистрация',
    'pages/login.php' => 'Вход',
    'admin/index.php' => 'Админ-панель',
    'admin/mining_packages.php' => 'Управление майнинг-пакетами'
];

foreach ($pages_to_check as $page => $description) {
    if (file_exists($page)) {
        echo "<p>✅ $description ($page) существует</p>";
    } else {
        echo "<p>❌ $description ($page) не найден</p>";
        $issues[] = "Страница $page отсутствует";
    }
}

// Итоговый результат
echo "<h2>🎯 Итоговый результат</h2>";

if ($all_good && empty($issues)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 ВСЕ ИСПРАВЛЕНИЯ РАБОТАЮТ!</h3>";
    echo "<p><strong>Система полностью готова к использованию:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Все функции работают корректно</li>";
    echo "<li>✅ База данных подключена</li>";
    echo "<li>✅ Дублирование функций устранено</li>";
    echo "<li>✅ Старые таблицы заменены на новые</li>";
    echo "<li>✅ Все страницы доступны</li>";
    echo "</ul>";
    echo "<p><strong>Можете использовать:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>🏠 Главная страница</a></li>";
    echo "<li><a href='pages/register.php' target='_blank'>📝 Регистрация</a></li>";
    echo "<li><a href='pages/login.php' target='_blank'>🔐 Вход в систему</a></li>";
    echo "<li><a href='pages/dashboard.php' target='_blank'>📊 Дашборд</a></li>";
    echo "<li><a href='pages/mining-packages.php' target='_blank'>⚡ Майнинг-пакеты</a></li>";
    echo "<li><a href='admin/index.php' target='_blank'>🔧 Админ-панель</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p><strong>Найденные проблемы:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "<p><strong>Рекомендации:</strong></p>";
    echo "<ul>";
    echo "<li>Запустите <a href='setup_database.php'>setup_database.php</a> для настройки БД</li>";
    echo "<li>Импортируйте схему из database/astrogenix_eco_mining.sql</li>";
    echo "<li>Проверьте права доступа к файлам</li>";
    echo "<li>Убедитесь, что OSPanel запущен</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . " | Версия: AstroGenix Eco Mining v1.0</small></p>";
?>
