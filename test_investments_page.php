<?php
/**
 * Тест страницы инвестиций AstroGenix
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест страницы инвестиций AstroGenix</h1>";

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. Тест функции getAllInvestments (совместимость)</h2>";

try {
    $investments = getAllInvestments(true);
    echo "<p>✅ getAllInvestments() работает: найдено " . count($investments) . " инвестиций</p>";
    
    if (!empty($investments)) {
        $investment = $investments[0];
        echo "<h3>Проверка полей первой инвестиции:</h3>";
        
        $required_fields = [
            'id' => 'ID',
            'title' => 'Название',
            'category' => 'Категория',
            'price' => 'Цена',
            'monthly_rate_min' => 'Минимальная месячная ставка',
            'monthly_rate_max' => 'Максимальная месячная ставка',
            'return_period_months' => 'Период возврата',
            'location' => 'Местоположение',
            'features' => 'Особенности',
            'capital_return' => 'Возврат капитала',
            'image_url' => 'URL изображения',
            'description' => 'Описание'
        ];
        
        $missing_fields = [];
        
        foreach ($required_fields as $field => $description) {
            if (isset($investment[$field])) {
                $value = $investment[$field];
                if ($value === null || $value === '') {
                    echo "<p>⚠️ <strong>$field</strong> ($description): пустое значение</p>";
                } else {
                    echo "<p>✅ <strong>$field</strong> ($description): " . (is_bool($value) ? ($value ? 'true' : 'false') : htmlspecialchars(substr(strval($value), 0, 50))) . "</p>";
                }
            } else {
                echo "<p>❌ <strong>$field</strong> ($description): отсутствует</p>";
                $missing_fields[] = $field;
            }
        }
        
        if (empty($missing_fields)) {
            echo "<p style='color: green; font-weight: bold;'>✅ Все необходимые поля присутствуют!</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ Отсутствуют поля: " . implode(', ', $missing_fields) . "</p>";
        }
    } else {
        echo "<p>⚠️ Нет инвестиций для тестирования</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка getAllInvestments: " . $e->getMessage() . "</p>";
}

echo "<h2>2. Тест функции getInvestmentById</h2>";

try {
    // Получаем первую инвестицию для тестирования
    if (!empty($investments)) {
        $test_id = $investments[0]['id'];
        $investment = getInvestmentById($test_id);
        
        if ($investment) {
            echo "<p>✅ getInvestmentById($test_id) работает</p>";
            echo "<p>Название: " . htmlspecialchars($investment['title']) . "</p>";
            echo "<p>Цена: " . formatCurrency($investment['price']) . "</p>";
            echo "<p>Местоположение: " . htmlspecialchars($investment['location']) . "</p>";
        } else {
            echo "<p>❌ getInvestmentById($test_id) вернула null</p>";
        }
    } else {
        echo "<p>⚠️ Нет инвестиций для тестирования getInvestmentById</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Ошибка getInvestmentById: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Тест вспомогательных функций</h2>";

// Тест formatCurrency
try {
    $formatted = formatCurrency(1234.56);
    echo "<p>✅ formatCurrency(1234.56): $formatted</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка formatCurrency: " . $e->getMessage() . "</p>";
}

// Тест formatPercentage
try {
    $formatted = formatPercentage(12.345);
    echo "<p>✅ formatPercentage(12.345): $formatted%</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка formatPercentage: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Тест данных майнинг-пакетов</h2>";

try {
    // Проверяем, есть ли майнинг-пакеты в БД
    $stmt = $db->query("SELECT COUNT(*) as count FROM mining_packages WHERE is_active = 1");
    $package_count = $stmt->fetch()['count'];
    echo "<p>✅ Активных майнинг-пакетов в БД: $package_count</p>";
    
    if ($package_count > 0) {
        // Получаем один пакет для проверки
        $stmt = $db->query("SELECT * FROM mining_packages WHERE is_active = 1 LIMIT 1");
        $package = $stmt->fetch();
        
        echo "<h3>Пример майнинг-пакета из БД:</h3>";
        echo "<ul>";
        echo "<li>ID: " . $package['id'] . "</li>";
        echo "<li>Название: " . htmlspecialchars($package['name']) . "</li>";
        echo "<li>Тип энергии: " . htmlspecialchars($package['energy_type']) . "</li>";
        echo "<li>Минимальная инвестиция: " . formatCurrency($package['min_investment']) . "</li>";
        echo "<li>Дневная ставка: " . formatPercentage($package['daily_rate']) . "%</li>";
        echo "<li>Длительность: " . $package['duration_days'] . " дней</li>";
        echo "<li>Местоположение: " . htmlspecialchars($package['location'] ?: 'Не указано') . "</li>";
        echo "</ul>";
    } else {
        echo "<p>⚠️ Нет активных майнинг-пакетов в БД</p>";
        echo "<p>Рекомендация: Импортируйте тестовые данные или создайте майнинг-пакеты через админ-панель</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка проверки майнинг-пакетов: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Проверка страницы investments.php</h2>";

if (file_exists('pages/investments.php')) {
    echo "<p>✅ Файл pages/investments.php существует</p>";
    
    // Проверяем, можно ли включить страницу без ошибок
    ob_start();
    $old_error_reporting = error_reporting(0); // Временно отключаем вывод ошибок
    
    try {
        // Симулируем переменные, которые ожидает страница
        $_GET['category'] = '';
        $_GET['sort'] = 'created_at';
        $_GET['order'] = 'DESC';
        
        include 'pages/investments.php';
        $page_content = ob_get_contents();
        
        if (strpos($page_content, 'Warning') === false && strpos($page_content, 'Error') === false) {
            echo "<p>✅ Страница investments.php загружается без ошибок</p>";
        } else {
            echo "<p>⚠️ Страница investments.php содержит предупреждения</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Ошибка загрузки страницы investments.php: " . $e->getMessage() . "</p>";
    }
    
    error_reporting($old_error_reporting);
    ob_end_clean();
    
} else {
    echo "<p>❌ Файл pages/investments.php не найден</p>";
}

// Итоговый результат
echo "<h2>🎯 Результат тестирования</h2>";

$all_good = true;

// Проверяем основные условия
if (empty($investments)) {
    $all_good = false;
}

if (isset($missing_fields) && !empty($missing_fields)) {
    $all_good = false;
}

if ($all_good) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Страница инвестиций готова к работе!</h3>";
    echo "<p><strong>✅ Исправления:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Все необходимые поля добавлены в функции совместимости</li>";
    echo "<li>✅ getAllInvestments() возвращает полные данные</li>";
    echo "<li>✅ getInvestmentById() работает корректно</li>";
    echo "<li>✅ Функции форматирования доступны</li>";
    echo "</ul>";
    echo "<p><strong>🚀 Можете использовать:</strong></p>";
    echo "<ul>";
    echo "<li><a href='pages/investments.php' target='_blank'>💰 Страница инвестиций</a></li>";
    echo "<li><a href='pages/mining-packages.php' target='_blank'>⚡ Майнинг-пакеты</a></li>";
    echo "<li><a href='admin/mining_packages.php' target='_blank'>🔧 Управление пакетами</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Требуется дополнительная настройка</h3>";
    echo "<p>Обнаружены проблемы:</p>";
    echo "<ul>";
    if (empty($investments)) {
        echo "<li>Нет майнинг-пакетов в системе</li>";
    }
    if (isset($missing_fields) && !empty($missing_fields)) {
        echo "<li>Отсутствуют поля: " . implode(', ', $missing_fields) . "</li>";
    }
    echo "</ul>";
    echo "<p>Рекомендации:</p>";
    echo "<ul>";
    echo "<li>Импортируйте схему БД из database/schema.sql</li>";
    echo "<li>Создайте майнинг-пакеты через админ-панель</li>";
    echo "<li>Проверьте функции совместимости</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Время тестирования: " . date('Y-m-d H:i:s') . " | Тест страницы инвестиций</small></p>";
?>
