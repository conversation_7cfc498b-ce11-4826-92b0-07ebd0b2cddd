# 🚀 Быстрая установка AstroGenix на OSPanel

## 📋 Пошаговая инструкция

### Шаг 1: Подготовка OSPanel
1. **Запустите OSPanel** и убедитесь, что Apache и MySQL работают
2. **Скопируйте файлы проекта** в папку `C:\OSPanel\domains\genix\`
3. **Откройте браузер** и перейдите по адресу `http://genix/`

### Шаг 2: Настройка базы данных

#### Вариант А: Автоматическая настройка
1. Откройте в браузере: `http://genix/setup_database.php`
2. Следуйте инструкциям на экране
3. Импортируйте полную схему БД (см. Шаг 3)

#### Вариант Б: Ручная настройка
1. Откройте **phpMyAdmin**: `http://localhost/openserver/phpmyadmin/`
2. Создайте новую базу данных: `astrogenix_eco_mining`
3. Импортируйте файл: `database/astrogenix_eco_mining.sql`

### Шаг 3: Импорт полной схемы БД

#### Через phpMyAdmin:
1. Откройте `http://localhost/openserver/phpmyadmin/`
2. Выберите базу данных `astrogenix_eco_mining`
3. Перейдите на вкладку **"Импорт"**
4. Выберите файл `database/astrogenix_eco_mining.sql`
5. Нажмите **"Вперед"**

#### Через командную строку:
```bash
# Откройте командную строку в папке OSPanel
cd C:\OSPanel\modules\database\MySQL-8.0\bin

# Импортируйте схему БД
mysql.exe -u root -p astrogenix_eco_mining < C:\OSPanel\domains\genix\database\astrogenix_eco_mining.sql
```

### Шаг 4: Проверка установки
1. Откройте: `http://genix/`
2. Убедитесь, что страница загружается без ошибок
3. Проверьте работу анимаций и счетчиков

### Шаг 5: Создание администратора
1. Зарегистрируйтесь через: `http://genix/pages/register.php`
2. Откройте phpMyAdmin
3. Выполните SQL-запрос:
```sql
UPDATE users SET role = 'admin' WHERE id = 1;
```
4. Войдите в админ-панель: `http://genix/admin/`

## 🔧 Настройки для OSPanel

### Файл config/database.php уже настроен для OSPanel:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix_eco_mining');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### Если возникают ошибки подключения:
1. Проверьте, что MySQL запущен в OSPanel
2. Убедитесь, что база данных создана
3. Проверьте права доступа к файлам

## 📁 Структура проекта

```
genix/
├── admin/                  # Административная панель
├── assets/                 # CSS, JS, изображения
├── config/                 # Конфигурационные файлы
├── cron/                   # Cron-задачи
├── database/               # SQL файлы
├── includes/               # PHP функции
├── pages/                  # Страницы сайта
├── uploads/                # Загруженные файлы
├── index.php               # Главная страница
├── setup_database.php     # Скрипт настройки БД
└── INSTALL_OSPANEL.md     # Эта инструкция
```

## 🎯 Тестирование функций

### Основные страницы:
- ✅ `http://genix/` - Главная страница
- ✅ `http://genix/pages/register.php` - Регистрация
- ✅ `http://genix/pages/login.php` - Вход
- ✅ `http://genix/pages/mining-packages.php` - Майнинг-пакеты
- ✅ `http://genix/pages/rankings.php` - Рейтинги
- ✅ `http://genix/admin/` - Админ-панель

### Функции для тестирования:
1. **Регистрация пользователя** с реферальным кодом
2. **Создание майнинг-пакетов** через админ-панель
3. **Публикация новостей** через админ-панель
4. **Работа анимаций** и живых счетчиков
5. **Мобильная адаптация** (F12 → Device Mode)

## 🚨 Решение проблем

### Ошибка "Failed to open stream":
```
Warning: require_once(database.php): Failed to open stream
```
**Решение:** Файлы уже исправлены, используются абсолютные пути

### Ошибка подключения к БД:
```
Connection error: SQLSTATE[HY000] [1049] Unknown database
```
**Решение:** Создайте базу данных через `setup_database.php`

### Белая страница или ошибки PHP:
1. Включите отображение ошибок в OSPanel
2. Проверьте логи Apache: `C:\OSPanel\userdata\logs\`
3. Убедитесь, что все файлы скопированы

### Не работают анимации:
1. Проверьте подключение к интернету (для CDN)
2. Убедитесь, что JavaScript включен
3. Откройте консоль браузера (F12) для проверки ошибок

## 📞 Поддержка

Если возникли проблемы:
1. Проверьте логи OSPanel
2. Убедитесь, что все файлы на месте
3. Проверьте настройки PHP (должен быть PHP 7.4+)
4. Убедитесь, что расширения PDO и PDO_MySQL включены

## 🎉 Готово!

После успешной установки у вас будет:
- ✅ Полнофункциональная экомайнинг-платформа
- ✅ Реферальная система с рейтингами
- ✅ Административная панель
- ✅ Красивые анимации и эффекты
- ✅ Мобильная адаптация

**Добро пожаловать в AstroGenix! 🌱⚡💚**
