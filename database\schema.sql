-- AstroGenix Eco Mining Database Schema
CREATE DATABASE IF NOT EXISTS astrogenix_mining;
USE astrogenix_mining;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL
);

-- Mining farms table
CREATE TABLE investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('solar', 'wind', 'hydro', 'geothermal', 'hybrid', 'other') NOT NULL DEFAULT 'solar',
    min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
    max_amount DECIMAL(15,2) NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    duration_months INT NULL,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User mining farms table
CREATE TABLE user_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    investment_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily profits table
CREATE TABLE daily_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(50) DEFAULT 'general',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User verifications table
CREATE TABLE user_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    passport_photo_path VARCHAR(500) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    admin_notes TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'admin', 'active', 0.00);

-- Insert sample eco mining farms
INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url) VALUES
('Nevada Solar Mining Farm', 'State-of-the-art solar-powered cryptocurrency mining facility in Nevada desert. 100% renewable energy with advanced cooling systems and maximum efficiency.', 'solar', 1000.00, 50000.00, 8.50, 24, 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=400'),
('Iceland Geothermal Mining Hub', 'Cutting-edge geothermal-powered mining operation in Iceland. Utilizing natural geothermal energy for sustainable cryptocurrency mining with zero carbon footprint.', 'geothermal', 2500.00, 100000.00, 12.00, 18, 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400'),
('Texas Wind Mining Complex', 'Large-scale wind-powered mining facility in Texas wind corridor. Advanced turbine technology ensures consistent power generation for optimal mining performance.', 'wind', 500.00, 25000.00, 7.25, 36, 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=400'),
('Norway Hydro Mining Station', 'Hydroelectric-powered mining station in Norway utilizing clean water energy. Environmentally sustainable with guaranteed uptime and excellent returns.', 'hydro', 1500.00, 75000.00, 10.50, 24, 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?w=400'),
('Canadian Hybrid Green Farm', 'Multi-source renewable energy mining farm combining solar, wind, and hydro power. Diversified energy portfolio ensures maximum stability and returns.', 'hybrid', 100.00, 10000.00, 6.75, 12, 'https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400'),
('Costa Rica Eco Mining Park', 'Tropical eco-friendly mining park powered entirely by renewable sources. Carbon-negative operations with rainforest conservation initiatives.', 'solar', 250.00, 15000.00, 15.00, 6, 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, image_url, is_published) VALUES
('Green Mining Revolution 2024', 'green-mining-revolution-2024', 'The sustainable cryptocurrency mining industry continues to show exponential growth in 2024, with renewable energy-powered farms leading the charge. Our analysis shows that solar and wind-powered mining operations are particularly attractive for eco-conscious investors seeking consistent returns while protecting the environment.', 'Analysis of current green mining trends and sustainable investment opportunities.', '/assets/images/blog-greenmining.jpg', TRUE),
('Renewable Energy Mining Growth', 'renewable-energy-mining-growth', 'The renewable energy mining sector has experienced unprecedented growth over the past year, with demand for carbon-neutral cryptocurrency mining reaching new heights. This presents excellent opportunities for investors looking to diversify their portfolio with environmentally responsible high-yield assets.', 'Exploring the booming eco-friendly mining market and investment potential.', '/assets/images/blog-renewable.jpg', TRUE);
