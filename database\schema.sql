-- AstroGenix Eco Mining Database Schema
-- Complete database structure for sustainable cryptocurrency mining platform
CREATE DATABASE IF NOT EXISTS astrogenix_mining CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE astrogenix_mining;

-- Users table with referral system integration
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    referred_by INT NULL COMMENT 'ID of user who referred this user',
    total_mined DECIMAL(15,2) DEFAULT 0.00,
    total_co2_saved DECIMAL(10,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_referral_code (referral_code),
    INDEX idx_referred_by (referred_by)
);

-- Mining packages table (replacing investments)
CREATE TABLE mining_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    energy_type ENUM('solar', 'wind', 'hydro', 'geothermal', 'hybrid', 'biomass') NOT NULL DEFAULT 'solar',
    min_investment DECIMAL(15,2) NOT NULL DEFAULT 10.00,
    max_investment DECIMAL(15,2) NULL,
    daily_rate DECIMAL(5,4) NOT NULL COMMENT 'Daily mining rate percentage',
    duration_days INT NOT NULL DEFAULT 365,
    hash_power VARCHAR(50) NOT NULL COMMENT 'Mining hash power (e.g., 100 TH/s)',
    energy_consumption DECIMAL(10,2) NOT NULL COMMENT 'kWh per day',
    co2_saved DECIMAL(10,2) NOT NULL COMMENT 'CO2 saved per day in kg',
    location VARCHAR(255) NOT NULL,
    image_url VARCHAR(500),
    features JSON COMMENT 'Package features and benefits',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User mining investments table
CREATE TABLE user_mining_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,4) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_mined DECIMAL(15,2) DEFAULT 0.00,
    total_co2_saved DECIMAL(10,2) DEFAULT 0.00,
    hash_power_allocated VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES mining_packages(id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_dates (start_date, end_date)
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily mining profits table
CREATE TABLE daily_mining_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    mined_amount DECIMAL(15,2) NOT NULL,
    co2_saved DECIMAL(10,2) NOT NULL,
    energy_used DECIMAL(10,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_mining_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date),
    INDEX idx_profit_date (profit_date)
);

-- Referral system table
CREATE TABLE referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL COMMENT 'User who referred',
    referred_id INT NOT NULL COMMENT 'User who was referred',
    referral_code VARCHAR(20) NOT NULL UNIQUE,
    commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission percentage',
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referrer_id, referred_id),
    INDEX idx_referrer (referrer_id),
    INDEX idx_code (referral_code)
);

-- Referral earnings table
CREATE TABLE referral_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referral_id INT NOT NULL,
    earning_amount DECIMAL(15,2) NOT NULL,
    source_investment_id INT NOT NULL COMMENT 'Investment that generated the commission',
    earning_type ENUM('signup_bonus', 'investment_commission', 'mining_commission') NOT NULL,
    earning_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referral_id) REFERENCES referrals(id) ON DELETE CASCADE,
    FOREIGN KEY (source_investment_id) REFERENCES user_mining_investments(id) ON DELETE CASCADE,
    INDEX idx_referral_date (referral_id, earning_date),
    INDEX idx_earning_date (earning_date)
);

-- Eco mining news table
CREATE TABLE eco_news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category ENUM('solar', 'wind', 'hydro', 'geothermal', 'technology', 'environment', 'general') DEFAULT 'general',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    author_id INT,
    views_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_published (is_published, published_at),
    INDEX idx_category (category),
    INDEX idx_featured (is_featured)
);

-- Mining rankings table
CREATE TABLE mining_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_mined DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_co2_saved DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_energy_used DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    active_investments_count INT NOT NULL DEFAULT 0,
    rank_position INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_ranking (user_id),
    INDEX idx_rank (rank_position),
    INDEX idx_total_mined (total_mined DESC)
);

-- Referral rankings table
CREATE TABLE referral_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_referrals INT NOT NULL DEFAULT 0,
    total_referral_earnings DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    active_referrals INT NOT NULL DEFAULT 0,
    rank_position INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_ref_ranking (user_id),
    INDEX idx_ref_rank (rank_position),
    INDEX idx_total_referrals (total_referrals DESC),
    INDEX idx_total_earnings (total_referral_earnings DESC)
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User verifications table
CREATE TABLE user_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    passport_photo_path VARCHAR(500) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    admin_notes TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, referral_code, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMIN001', TRUE, 'admin', 'active', 0.00);

-- Insert sample eco mining packages
INSERT INTO mining_packages (name, description, energy_type, min_investment, max_investment, daily_rate, duration_days, hash_power, energy_consumption, co2_saved, location, image_url, features) VALUES
('Nevada Solar Pro', 'State-of-the-art solar-powered cryptocurrency mining facility in Nevada desert. 100% renewable energy with advanced cooling systems and maximum efficiency.', 'solar', 1000.00, 50000.00, 0.0028, 730, '100 TH/s', 2400.00, 1200.00, 'Nevada, USA', 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=400', '["24/7 monitoring", "Advanced cooling", "Insurance included", "Real-time stats"]'),
('Iceland Geothermal Elite', 'Cutting-edge geothermal-powered mining operation in Iceland. Utilizing natural geothermal energy for sustainable cryptocurrency mining with zero carbon footprint.', 'geothermal', 2500.00, 100000.00, 0.0040, 540, '250 TH/s', 3600.00, 1800.00, 'Reykjavik, Iceland', 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400', '["Zero emissions", "Geothermal power", "Premium support", "Guaranteed uptime"]'),
('Texas Wind Power', 'Large-scale wind-powered mining facility in Texas wind corridor. Advanced turbine technology ensures consistent power generation for optimal mining performance.', 'wind', 500.00, 25000.00, 0.0024, 1095, '75 TH/s', 1800.00, 900.00, 'Texas, USA', 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=400', '["Wind powered", "Stable returns", "Long term", "Eco friendly"]'),
('Norway Hydro Station', 'Hydroelectric-powered mining station in Norway utilizing clean water energy. Environmentally sustainable with guaranteed uptime and excellent returns.', 'hydro', 1500.00, 75000.00, 0.0035, 720, '150 TH/s', 2700.00, 1350.00, 'Bergen, Norway', 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?w=400', '["Hydro power", "High efficiency", "Stable income", "Green energy"]'),
('Canadian Hybrid Farm', 'Multi-source renewable energy mining farm combining solar, wind, and hydro power. Diversified energy portfolio ensures maximum stability and returns.', 'hybrid', 100.00, 10000.00, 0.0022, 365, '50 TH/s', 1200.00, 600.00, 'Alberta, Canada', 'https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400', '["Multi-source energy", "Diversified", "Beginner friendly", "Low minimum"]'),
('Costa Rica Eco Park', 'Tropical eco-friendly mining park powered entirely by renewable sources. Carbon-negative operations with rainforest conservation initiatives.', 'solar', 250.00, 15000.00, 0.0050, 180, '200 TH/s', 4800.00, 2400.00, 'San José, Costa Rica', 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400', '["Carbon negative", "Rainforest conservation", "High returns", "Short term"]');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert sample eco news
INSERT INTO eco_news (title, slug, content, excerpt, category, image_url, is_published, is_featured, published_at) VALUES
('Green Mining Revolution 2024', 'green-mining-revolution-2024', 'The sustainable cryptocurrency mining industry continues to show exponential growth in 2024, with renewable energy-powered farms leading the charge. Our analysis shows that solar and wind-powered mining operations are particularly attractive for eco-conscious investors seeking consistent returns while protecting the environment. The integration of AI-powered energy management systems has increased efficiency by 40% while reducing carbon emissions to zero.', 'Analysis of current green mining trends and sustainable investment opportunities.', 'technology', 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=600', TRUE, TRUE, NOW()),
('Solar Mining Breakthrough', 'solar-mining-breakthrough', 'Scientists at AstroGenix have developed revolutionary solar panel technology that increases mining efficiency by 60% while maintaining zero carbon footprint. This breakthrough technology is now being deployed across all our solar mining facilities worldwide.', 'Revolutionary solar technology increases mining efficiency by 60%.', 'solar', 'https://images.unsplash.com/photo-1508514177221-188b1cf16e9d?w=600', TRUE, TRUE, NOW()),
('Wind Power Mining Expansion', 'wind-power-mining-expansion', 'Our wind-powered mining operations are expanding to new locations across Europe and North America. The latest wind turbine technology ensures consistent power generation even in low-wind conditions, making our mining operations more reliable than ever.', 'Wind-powered mining operations expanding globally with new technology.', 'wind', 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=600', TRUE, FALSE, NOW()),
('Hydro Mining Efficiency Record', 'hydro-mining-efficiency-record', 'Our Norwegian hydro-powered mining facility has set a new world record for energy efficiency in cryptocurrency mining. The facility achieved 99.8% uptime while maintaining completely carbon-neutral operations throughout the year.', 'Norwegian facility sets new world record for mining efficiency.', 'hydro', 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?w=600', TRUE, FALSE, NOW()),
('Carbon Negative Mining Achievement', 'carbon-negative-mining-achievement', 'AstroGenix becomes the first mining platform to achieve carbon-negative operations across all facilities. Through innovative carbon capture technology and reforestation programs, we now remove more CO2 from the atmosphere than we produce.', 'AstroGenix achieves carbon-negative mining operations worldwide.', 'environment', 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600', TRUE, TRUE, NOW());

-- Insert sample demo users with referral codes
INSERT INTO users (username, email, password_hash, referral_code, total_mined, total_co2_saved, status) VALUES
('eco_miner_1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ECO001', 15420.50, 7710.25, 'active'),
('green_investor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'GREEN01', 28750.75, 14375.38, 'active'),
('solar_pioneer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'SOLAR01', 42180.25, 21090.13, 'active'),
('wind_warrior', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'WIND001', 33650.00, 16825.00, 'active'),
('hydro_hero', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'HYDRO01', 38920.80, 19460.40, 'active');

-- Insert mining rankings for demo users
INSERT INTO mining_rankings (user_id, total_mined, total_co2_saved, total_energy_used, active_investments_count, rank_position) VALUES
(4, 42180.25, 21090.13, 84360.50, 3, 1),  -- solar_pioneer
(6, 38920.80, 19460.40, 77841.60, 2, 2),  -- hydro_hero
(5, 33650.00, 16825.00, 67300.00, 2, 3),  -- wind_warrior
(3, 28750.75, 14375.38, 57501.50, 1, 4),  -- green_investor
(2, 15420.50, 7710.25, 30841.00, 1, 5);  -- eco_miner_1

-- Insert referral rankings (assuming some users have referrals)
INSERT INTO referral_rankings (user_id, total_referrals, total_referral_earnings, active_referrals, rank_position) VALUES
(4, 15, 2850.75, 12, 1),  -- solar_pioneer
(3, 8, 1420.50, 7, 2),   -- green_investor
(6, 5, 980.25, 4, 3),    -- hydro_hero
(5, 3, 450.00, 3, 4),    -- wind_warrior
(2, 1, 125.50, 1, 5);    -- eco_miner_1

-- Insert site settings for AstroGenix
INSERT INTO site_settings (setting_key, setting_value, setting_type) VALUES
('site_name', 'AstroGenix', 'text'),
('site_description', 'The world\'s leading eco-friendly cryptocurrency mining platform powered by 100% renewable energy', 'text'),
('site_logo', '/assets/images/logo.png', 'text'),
('maintenance_mode', '0', 'boolean'),
('registration_enabled', '1', 'boolean'),
('min_deposit', '10', 'number'),
('max_deposit', '100000', 'number'),
('min_withdrawal', '10', 'number'),
('referral_commission', '10', 'number'),
('signup_bonus', '25', 'number'),
('total_co2_saved', '2500000', 'number'),
('total_energy_generated', '5000000', 'number'),
('total_miners', '15000', 'number'),
('platform_efficiency', '99.8', 'number');
