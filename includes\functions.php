<?php
// Core functions for AstroGenix Eco Mining Platform
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/referral_functions.php';
require_once __DIR__ . '/mining_functions.php';

// User functions
function getUserById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($username, $email, $password, $referral_code = null) {
    $db = getDB();
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    // Generate unique referral code for new user
    $user_referral_code = generateReferralCode($username);

    // Ensure referral code is unique
    $counter = 1;
    $original_code = $user_referral_code;
    while (getUserByReferralCode($user_referral_code)) {
        $user_referral_code = $original_code . $counter;
        $counter++;
    }

    try {
        $db->beginTransaction();

        // Get referrer if referral code provided
        $referrer_id = null;
        if ($referral_code) {
            $referrer = getUserByReferralCode($referral_code);
            if ($referrer) {
                $referrer_id = $referrer['id'];
            }
        }

        // Create user with referral code
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password_hash, referral_code, referred_by)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$username, $email, $password_hash, $user_referral_code, $referrer_id]);
        $user_id = $db->lastInsertId();

        // Create referral relationship if referred by someone
        if ($referrer_id) {
            createReferral($referrer_id, $user_id, $user_referral_code);
            // Process signup bonus
            processSignupBonus($referrer_id, $user_id);
        }

        $db->commit();
        return $user_id;

    } catch (Exception $e) {
        $db->rollBack();
        error_log("User creation error: " . $e->getMessage());
        return false;
    }
}

function updateUserBalance($user_id, $amount, $operation = 'add') {
    $db = getDB();
    
    if ($operation === 'add') {
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
    }
    
    return $stmt->execute([$amount, $user_id]);
}

// Investment functions (deprecated - use getAllMiningPackages instead)
function getAllInvestments($active_only = true) {
    // Redirect to new mining packages function for compatibility
    if (!function_exists('getAllMiningPackages')) {
        require_once __DIR__ . '/mining_functions.php';
    }

    try {
        $packages = getAllMiningPackages($active_only ? null : 999);

        // Convert mining packages to investment format for compatibility
        $investments = [];
        foreach ($packages as $package) {
            $monthly_rate = $package['daily_rate'] * 30; // Convert daily to monthly
            $investments[] = [
                'id' => $package['id'],
                'title' => $package['name'],
                'category' => $package['energy_type'],
                'min_amount' => $package['min_investment'],
                'max_amount' => $package['max_investment'],
                'price' => $package['min_investment'], // For compatibility
                'monthly_rate' => $monthly_rate,
                'monthly_rate_min' => $monthly_rate * 0.8, // Estimated range
                'monthly_rate_max' => $monthly_rate * 1.2,
                'daily_rate' => $package['daily_rate'],
                'duration_months' => ceil($package['duration_days'] / 30),
                'return_period_months' => ceil($package['duration_days'] / 30),
                'duration_days' => $package['duration_days'],
                'image_url' => $package['image_url'] ?: '/assets/images/default-mining.jpg',
                'description' => $package['description'] ?: 'Экологический майнинг-пакет',
                'features' => is_string($package['features']) ? $package['features'] : json_encode($package['features'] ?: []),
                'location' => $package['location'] ?: 'Global',
                'hash_power' => $package['hash_power'] ?: 'N/A',
                'energy_consumption' => $package['energy_consumption'] ?: 0,
                'co2_saved' => $package['co2_saved'] ?: 0,
                'capital_return' => true, // Assume capital return for eco mining
                'is_active' => $package['is_active'],
                'created_at' => $package['created_at']
            ];
        }

        return $investments;

    } catch (Exception $e) {
        error_log("getAllInvestments compatibility error: " . $e->getMessage());
        return [];
    }
}

function getInvestmentById($id) {
    // Redirect to new mining package function for compatibility
    if (!function_exists('getMiningPackageById')) {
        require_once __DIR__ . '/mining_functions.php';
    }

    try {
        $package = getMiningPackageById($id);

        if ($package) {
            // Convert mining package to investment format for compatibility
            $monthly_rate = $package['daily_rate'] * 30; // Convert daily to monthly
            return [
                'id' => $package['id'],
                'title' => $package['name'],
                'category' => $package['energy_type'],
                'min_amount' => $package['min_investment'],
                'max_amount' => $package['max_investment'],
                'price' => $package['min_investment'], // For compatibility
                'monthly_rate' => $monthly_rate,
                'monthly_rate_min' => $monthly_rate * 0.8, // Estimated range
                'monthly_rate_max' => $monthly_rate * 1.2,
                'daily_rate' => $package['daily_rate'],
                'duration_months' => ceil($package['duration_days'] / 30),
                'return_period_months' => ceil($package['duration_days'] / 30),
                'duration_days' => $package['duration_days'],
                'image_url' => $package['image_url'] ?: '/assets/images/default-mining.jpg',
                'description' => $package['description'] ?: 'Экологический майнинг-пакет',
                'features' => is_string($package['features']) ? $package['features'] : json_encode($package['features'] ?: []),
                'location' => $package['location'] ?: 'Global',
                'hash_power' => $package['hash_power'] ?: 'N/A',
                'energy_consumption' => $package['energy_consumption'] ?: 0,
                'co2_saved' => $package['co2_saved'] ?: 0,
                'capital_return' => true, // Assume capital return for eco mining
                'is_active' => $package['is_active'],
                'created_at' => $package['created_at']
            ];
        }

        return null;

    } catch (Exception $e) {
        error_log("getInvestmentById compatibility error: " . $e->getMessage());
        return null;
    }
}

// Add compatibility fields for investments to work with old frontend code
function addInvestmentCompatibilityFields($investment) {
    if (!$investment) return $investment;

    // Add missing fields with default values or computed values
    $investment['price'] = $investment['max_amount'] ?? $investment['min_amount'];
    $investment['monthly_rate_min'] = $investment['monthly_rate'];
    $investment['monthly_rate_max'] = $investment['monthly_rate'];
    $investment['return_period_months'] = $investment['duration_months'] ?? 12;

    // Ensure required fields exist with defaults
    if (!isset($investment['location'])) {
        $investment['location'] = ucfirst(str_replace('_', ' ', $investment['category'])) . ' Mining Farm';
    }
    if (!isset($investment['capital_return'])) {
        $investment['capital_return'] = true;
    }
    if (!isset($investment['features'])) {
        $investment['features'] = 'Eco-friendly mining, Renewable energy, Carbon neutral';
    }

    return $investment;
}

// Старая функция для совместимости (deprecated)
function createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $return_period_months = null) {
    // Проверяем, подключен ли файл с майнинг-функциями
    if (!function_exists('createMiningInvestment')) {
        require_once __DIR__ . '/mining_functions.php';
    }
    // Перенаправляем на новую функцию майнинг-инвестиций
    return createMiningInvestment($user_id, $investment_id, $amount);
}

function getUserInvestments($user_id) {
    $db = getDB();

    try {
        // Новая схема с майнинг-инвестициями
        $stmt = $db->prepare("
            SELECT umi.*, mp.name as package_name, mp.energy_type, mp.image_url, mp.daily_rate, mp.co2_saved
            FROM user_mining_investments umi
            JOIN mining_packages mp ON umi.package_id = mp.id
            WHERE umi.user_id = ?
            ORDER BY umi.created_at DESC
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        error_log("Get user investments error: " . $e->getMessage());
        return [];
    }
}

// Transaction functions
function createTransaction($user_id, $type, $amount, $screenshot_path = null, $wallet_address = null) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, screenshot_path, wallet_address, status)
        VALUES (?, ?, ?, ?, ?, 'pending')
    ");

    return $stmt->execute([$user_id, $type, $amount, $screenshot_path, $wallet_address]);
}

function getUserTransactions($user_id, $limit = 10) {
    $db = getDB();

    try {
        // Ensure limit is an integer and within reasonable bounds
        $limit = max(1, min(100, intval($limit)));

        // Get regular transactions and daily mining profits combined
        $stmt = $db->prepare("
            (SELECT
                id,
                'transaction' as source_type,
                type,
                amount,
                status,
                created_at,
                wallet_address,
                screenshot_path,
                admin_notes
            FROM transactions
            WHERE user_id = ?)
            UNION ALL
            (SELECT
                dmp.id,
                'mining_profit' as source_type,
                'mining_profit' as type,
                dmp.profit_amount as amount,
                'completed' as status,
                dmp.created_at,
                NULL as wallet_address,
                NULL as screenshot_path,
                CONCAT('Mining profit from package #', umi.package_id, ' (', mp.name, ')') as admin_notes
            FROM daily_mining_profits dmp
            JOIN user_mining_investments umi ON dmp.investment_id = umi.id
            JOIN mining_packages mp ON umi.package_id = mp.id
            WHERE umi.user_id = ?)
            ORDER BY created_at DESC
            LIMIT " . $limit
        );
        $stmt->execute([$user_id, $user_id]);
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        error_log("Get user transactions error: " . $e->getMessage());

        // Fallback: return only regular transactions if mining tables don't exist
        try {
            $stmt = $db->prepare("
                SELECT
                    id,
                    'transaction' as source_type,
                    type,
                    amount,
                    status,
                    created_at,
                    wallet_address,
                    screenshot_path,
                    admin_notes
                FROM transactions
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT " . $limit
            );
            $stmt->execute([$user_id]);
            return $stmt->fetchAll();
        } catch (PDOException $e2) {
            error_log("Get transactions fallback error: " . $e2->getMessage());
            return [];
        }
    }
}

function getPendingTransactions($type = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, u.username, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'pending'
    ";
    
    if ($type) {
        $sql .= " AND t.type = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute([$type]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute();
    }
    
    return $stmt->fetchAll();
}

function updateTransactionStatus($transaction_id, $status, $admin_notes = null) {
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, admin_notes = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    
    return $stmt->execute([$status, $admin_notes, $transaction_id]);
}

// Daily profit calculation
function calculateDailyProfit($user_investment_id, $amount, $monthly_rate) {
    $daily_rate = $monthly_rate / 30;
    return ($amount * $daily_rate) / 100;
}

// Старая функция для совместимости (deprecated)
function addDailyProfit($user_investment_id, $profit_amount, $date = null) {
    // Перенаправляем на новую функцию майнинг-прибылей
    return addDailyMiningProfit($user_investment_id, $profit_amount, 0, $date);
}

// Новая функция для добавления ежедневной майнинг-прибыли
function addDailyMiningProfit($investment_id, $profit_amount, $co2_saved = 0, $date = null) {
    $db = getDB();

    try {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $db->prepare("
            INSERT IGNORE INTO daily_mining_profits (investment_id, profit_amount, co2_saved, profit_date)
            VALUES (?, ?, ?, ?)
        ");

        return $stmt->execute([$investment_id, $profit_amount, $co2_saved, $date]);

    } catch (PDOException $e) {
        error_log("Add daily mining profit error: " . $e->getMessage());
        return false;
    }
}

// Blog functions (deprecated - use getEcoNews instead)
function getBlogPosts($published_only = true, $limit = null) {
    // Redirect to eco news function for compatibility
    if (!function_exists('getEcoNews')) {
        require_once __DIR__ . '/mining_functions.php';
    }

    try {
        return getEcoNews($limit ?: 10, false);
    } catch (Exception $e) {
        error_log("getBlogPosts compatibility error: " . $e->getMessage());
        return [];
    }
}

// Rankings functions moved to referral_functions.php to avoid duplication

// getReferralRankings moved to referral_functions.php to avoid duplication

// Referral program functions
function updateUserReferralCode($user_id, $referral_code) {
    $db = getDB();

    try {
        $stmt = $db->prepare("UPDATE users SET referral_code = ? WHERE id = ?");
        return $stmt->execute([$referral_code, $user_id]);
    } catch (PDOException $e) {
        error_log("Update referral code error: " . $e->getMessage());
        return false;
    }
}

function getReferralStats($user_id) {
    $db = getDB();

    try {
        $stmt = $db->prepare("
            SELECT
                COUNT(DISTINCT r.referred_user_id) as total_referrals,
                COUNT(DISTINCT CASE WHEN ru.is_active = 1 THEN r.referred_user_id END) as active_referrals,
                COALESCE(SUM(re.amount), 0) as total_earnings,
                COALESCE(SUM(CASE WHEN re.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) THEN re.amount ELSE 0 END), 0) as monthly_earnings
            FROM referrals r
            LEFT JOIN referral_earnings re ON r.id = re.referral_id
            LEFT JOIN users ru ON r.referred_user_id = ru.id
            WHERE r.referrer_user_id = ?
        ");

        $stmt->execute([$user_id]);
        return $stmt->fetch() ?: [
            'total_referrals' => 0,
            'active_referrals' => 0,
            'total_earnings' => 0,
            'monthly_earnings' => 0
        ];

    } catch (PDOException $e) {
        error_log("Get referral stats error: " . $e->getMessage());
        return [
            'total_referrals' => 0,
            'active_referrals' => 0,
            'total_earnings' => 0,
            'monthly_earnings' => 0
        ];
    }
}

function getReferralEarnings($user_id, $limit = 50) {
    $db = getDB();

    try {
        $stmt = $db->prepare("
            SELECT
                re.*,
                ru.username as referred_username
            FROM referral_earnings re
            JOIN referrals r ON re.referral_id = r.id
            LEFT JOIN users ru ON r.referred_user_id = ru.id
            WHERE r.referrer_user_id = ?
            ORDER BY re.created_at DESC
            LIMIT ?
        ");

        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        error_log("Get referral earnings error: " . $e->getMessage());
        return [];
    }
}

function getMyReferrals($user_id) {
    $db = getDB();

    try {
        $stmt = $db->prepare("
            SELECT
                ru.*,
                COUNT(umi.id) as total_investments,
                COALESCE(SUM(re.amount), 0) as my_earnings
            FROM referrals r
            JOIN users ru ON r.referred_user_id = ru.id
            LEFT JOIN user_mining_investments umi ON ru.id = umi.user_id
            LEFT JOIN referral_earnings re ON r.id = re.referral_id
            WHERE r.referrer_user_id = ?
            GROUP BY ru.id, ru.username, ru.email, ru.created_at, ru.is_active
            ORDER BY ru.created_at DESC
        ");

        $stmt->execute([$user_id]);
        return $stmt->fetchAll();

    } catch (PDOException $e) {
        error_log("Get my referrals error: " . $e->getMessage());
        return [];
    }
}

function getBlogPostBySlug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND is_published = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Contact functions
function saveContactMessage($name, $email, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO contact_messages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Statistics functions
function getDashboardStats($user_id) {
    $db = getDB();

    try {
        // Get user balance
        $user = getUserById($user_id);
        $balance = $user['balance'] ?? 0;

        // Get total invested amount (новая таблица user_mining_investments)
        $stmt = $db->prepare("SELECT SUM(amount) as total_invested FROM user_mining_investments WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$user_id]);
        $total_invested = $stmt->fetch()['total_invested'] ?? 0;

        // Get total profit earned (новая таблица daily_mining_profits)
        $stmt = $db->prepare("
            SELECT SUM(dmp.profit_amount) as total_profit
            FROM daily_mining_profits dmp
            JOIN user_mining_investments umi ON dmp.investment_id = umi.id
            WHERE umi.user_id = ?
        ");
        $stmt->execute([$user_id]);
        $total_profit = $stmt->fetch()['total_profit'] ?? 0;

        // Get active investments count
        $stmt = $db->prepare("SELECT COUNT(*) as active_investments FROM user_mining_investments WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$user_id]);
        $active_investments = $stmt->fetch()['active_investments'] ?? 0;

        // Get total CO2 saved
        $stmt = $db->prepare("
            SELECT SUM(mp.co2_saved * DATEDIFF(COALESCE(umi.end_date, CURDATE()), umi.start_date)) as total_co2_saved
            FROM user_mining_investments umi
            JOIN mining_packages mp ON umi.package_id = mp.id
            WHERE umi.user_id = ? AND umi.is_active = 1
        ");
        $stmt->execute([$user_id]);
        $total_co2_saved = $stmt->fetch()['total_co2_saved'] ?? 0;

        return [
            'balance' => floatval($balance),
            'total_invested' => floatval($total_invested),
            'total_profit' => floatval($total_profit),
            'active_investments' => intval($active_investments),
            'total_co2_saved' => floatval($total_co2_saved)
        ];

    } catch (PDOException $e) {
        error_log("Dashboard stats error: " . $e->getMessage());
        // Возвращаем значения по умолчанию при ошибке
        return [
            'balance' => 0.0,
            'total_invested' => 0.0,
            'total_profit' => 0.0,
            'active_investments' => 0,
            'total_co2_saved' => 0.0
        ];
    }
}

// Withdrawal processing function

function processWithdrawalRequest($user_id, $amount, $wallet_address) {
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum withdrawal amount is 10 USDT'];
    }

    if ($amount > $balance) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }

    if (empty($wallet_address)) {
        return ['success' => false, 'message' => 'USDT wallet address is required'];
    }

    // Create withdrawal transaction directly with new schema
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, wallet_address, status)
        VALUES (?, 'withdrawal', ?, ?, 'pending')
    ");

    if ($stmt->execute([$user_id, $amount, $wallet_address])) {
        return ['success' => true, 'message' => 'Withdrawal request submitted successfully. It will be processed within 24-48 hours.'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit withdrawal request. Please try again.'];
    }
}

// Formatting functions
function formatCurrency($amount) {
    return number_format(floatval($amount), 2) . ' USDT';
}

function formatPercentage($percentage) {
    return number_format(floatval($percentage), 2);
}

// CSRF Protection functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Verification functions
function getUserVerificationStatus($user_id) {
    $user = getUserById($user_id);
    return $user['verification_status'] ?? 'unverified';
}

function isUserVerified($user_id) {
    return getUserVerificationStatus($user_id) === 'verified';
}

function getUserVerification($user_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM user_verifications WHERE user_id = ? ORDER BY submitted_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

function submitVerificationRequest($user_id, $first_name, $last_name, $birth_date, $passport_file) {
    // Upload passport photo
    $uploadResult = uploadPassportPhoto($passport_file);

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    $db = getDB();

    try {
        $db->beginTransaction();

        // Insert verification request
        $stmt = $db->prepare("
            INSERT INTO user_verifications (user_id, first_name, last_name, birth_date, passport_photo_path, status)
            VALUES (?, ?, ?, ?, ?, 'pending')
        ");

        if (!$stmt->execute([$user_id, $first_name, $last_name, $birth_date, $uploadResult['file_path']])) {
            throw new Exception('Failed to insert verification request');
        }

        // Update user verification status
        $stmt = $db->prepare("UPDATE users SET verification_status = 'pending' WHERE id = ?");
        if (!$stmt->execute([$user_id])) {
            throw new Exception('Failed to update user verification status');
        }

        $db->commit();

        return ['success' => true, 'message' => 'Verification request submitted successfully. Please wait for admin approval.'];

    } catch (Exception $e) {
        $db->rollback();

        // Delete uploaded file if database insert fails
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
        }

        logError("Verification submission error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to submit verification request. Please try again.'];
    }
}

function uploadPassportPhoto($file) {
    $uploadDir = 'uploads/verifications/';

    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload JPG, PNG, or GIF image.'];
    }

    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'passport_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $fullPath = __DIR__ . '/../' . $filepath;

    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return [
            'success' => true,
            'file_path' => $filepath,
            'full_path' => $fullPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file'];
    }
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash_messages'][$type])) {
        $message = $_SESSION['flash_messages'][$type];
        unset($_SESSION['flash_messages'][$type]);
        return $message;
    }
    return null;
}

function displayFlashMessages() {
    $success_message = getFlashMessage('success');
    $error_message = getFlashMessage('error');
    $info_message = getFlashMessage('info');

    if ($success_message): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif;

    if ($error_message): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif;

    if ($info_message): ?>
        <div class="mb-6 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-info-circle mr-2"></i><?php echo htmlspecialchars($info_message); ?>
        </div>
    <?php endif;
}

// Redirect function
function redirect($url) {
    header("Location: $url");
    exit();
}

// Blog image functions
function getBlogPostImage($post, $size = 'medium') {
    // Default fallback image
    $default_image = 'https://lim-english.com/uploads/images/all/img_articles_new/news_in_english.jpg';

    // Check if post has image_url key and it's not empty
    if (isset($post['image_url']) && !empty($post['image_url'])) {
        return $post['image_url'];
    }

    return $default_image;
}

function getEscapedBlogImage($post, $size = 'medium') {
    return htmlspecialchars(getBlogPostImage($post, $size));
}

// Investment image functions
function getInvestmentImage($investment, $size = 'medium') {
    // Default fallback image for eco mining farms
    $default_image = 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80';

    // Check if investment has image_url key and it's not empty
    if (isset($investment['image_url']) && !empty($investment['image_url'])) {
        return $investment['image_url'];
    }

    return $default_image;
}

function getEscapedInvestmentImage($investment, $size = 'medium') {
    return htmlspecialchars(getInvestmentImage($investment, $size));
}

// User status functions
function getUserStatus($user) {
    // Check for new status field first, then fallback to legacy is_suspended
    if (isset($user['status'])) {
        return $user['status'];
    } elseif (isset($user['is_suspended']) && $user['is_suspended']) {
        return 'suspended';
    } else {
        return 'active';
    }
}

function getUserRole($user) {
    // Check for new role field first, then fallback to legacy is_admin
    if (isset($user['role'])) {
        return $user['role'];
    } elseif (isset($user['is_admin']) && $user['is_admin']) {
        return 'admin';
    } else {
        return 'user';
    }
}

function isUserSuspended($user) {
    return getUserStatus($user) === 'suspended';
}

function isUserAdmin($user) {
    return getUserRole($user) === 'admin';
}

// File upload functions
function uploadTransactionScreenshot($file) {
    // Use absolute path from document root
    $uploadDir = __DIR__ . '/../uploads/transactions/';
    $webPath = 'uploads/transactions/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Enhanced logging for debugging
    error_log("=== uploadTransactionScreenshot DEBUG ===");
    error_log("Upload dir: $uploadDir");
    error_log("File data: " . print_r($file, true));

    // Validate file
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        error_log("No tmp_name in file array");
        return ['success' => false, 'message' => 'No file uploaded - missing tmp_name'];
    }

    if (!is_uploaded_file($file['tmp_name'])) {
        error_log("File is not an uploaded file: " . $file['tmp_name']);
        return ['success' => false, 'message' => 'Invalid uploaded file'];
    }

    if ($file['error'] !== UPLOAD_ERR_OK) {
        error_log("Upload error: " . $file['error']);
        return ['success' => false, 'message' => 'File upload error: ' . $file['error']];
    }

    if ($file['size'] > $maxSize) {
        error_log("File too large: " . $file['size']);
        return ['success' => false, 'message' => 'File size exceeds 5MB limit'];
    }

    if (!in_array($file['type'], $allowedTypes)) {
        error_log("Invalid file type: " . $file['type']);
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed'];
    }

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            error_log("Failed to create upload directory: $uploadDir");
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
        error_log("Created upload directory: $uploadDir");
    }

    // Generate unique filename
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid('transaction_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $webFilepath = $webPath . $filename;

    error_log("Attempting to move file from {$file['tmp_name']} to $filepath");

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        error_log("File uploaded successfully to: $filepath");
        return ['success' => true, 'filepath' => $webFilepath, 'full_path' => $filepath];
    } else {
        error_log("Failed to move uploaded file");
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Process deposit request with screenshot
function processDepositRequest($user_id, $amount, $screenshot_file) {
    error_log("=== processDepositRequest DEBUG ===");
    error_log("User ID: $user_id");
    error_log("Amount: $amount");
    error_log("Screenshot file: " . print_r($screenshot_file, true));

    if ($amount < 10) {
        error_log("Amount too small: $amount");
        return ['success' => false, 'message' => 'Minimum deposit amount is $10'];
    }

    // Upload screenshot
    $uploadResult = uploadTransactionScreenshot($screenshot_file);
    error_log("Upload result: " . print_r($uploadResult, true));

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    try {
        $db = getDB();
        $stmt = $db->prepare("
            INSERT INTO transactions (user_id, type, amount, screenshot_path, status, created_at)
            VALUES (?, 'deposit', ?, ?, 'pending', NOW())
        ");

        $filepath = $uploadResult['filepath'];
        error_log("Inserting transaction with filepath: $filepath");

        if ($stmt->execute([$user_id, $amount, $filepath])) {
            $transaction_id = $db->lastInsertId();
            error_log("Transaction inserted successfully with ID: $transaction_id");
            return ['success' => true, 'message' => 'Deposit request submitted successfully. Please wait for admin approval.', 'transaction_id' => $transaction_id];
        } else {
            error_log("Failed to insert transaction");
            error_log("SQL Error: " . print_r($stmt->errorInfo(), true));

            // Delete uploaded file if database insert fails
            $fullPath = $uploadResult['full_path'] ?? null;
            if ($fullPath && file_exists($fullPath)) {
                unlink($fullPath);
                error_log("Deleted uploaded file due to DB error: $fullPath");
            }
            return ['success' => false, 'message' => 'Failed to submit deposit request. Please try again.'];
        }
    } catch (Exception $e) {
        error_log("Exception in processDepositRequest: " . $e->getMessage());

        // Delete uploaded file if exception occurs
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
            error_log("Deleted uploaded file due to exception: $fullPath");
        }
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Site settings functions
function getSiteSetting($key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function updateSiteSetting($key, $value, $type = 'text') {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO site_settings (setting_key, setting_value, setting_type)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP
    ");
    return $stmt->execute([$key, $value, $type, $value]);
}

function getAllSiteSettings() {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM site_settings ORDER BY setting_key");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}
?>
