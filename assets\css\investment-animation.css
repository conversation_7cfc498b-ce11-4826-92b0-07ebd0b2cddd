/* Investment Success Animation Styles */
.investment-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.investment-success-overlay.active {
    opacity: 1;
    visibility: visible;
}

.investment-success-content {
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    color: white;
    max-width: 500px;
    width: 90%;
    position: relative;
    overflow: hidden;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.investment-success-overlay.active .investment-success-content {
    transform: scale(1);
}

.eco-animation-container {
    position: relative;
    height: 200px;
    margin: 20px 0;
}

/* Floating leaves animation */
.leaf {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #22c55e;
    border-radius: 0 100% 0 100%;
    animation: floatLeaf 3s ease-in-out infinite;
}

.leaf:nth-child(1) { left: 10%; animation-delay: 0s; }
.leaf:nth-child(2) { left: 30%; animation-delay: 0.5s; }
.leaf:nth-child(3) { left: 50%; animation-delay: 1s; }
.leaf:nth-child(4) { left: 70%; animation-delay: 1.5s; }
.leaf:nth-child(5) { left: 90%; animation-delay: 2s; }

@keyframes floatLeaf {
    0% {
        transform: translateY(100px) rotate(0deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Energy pulse animation */
.energy-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 3px solid #fbbf24;
    border-radius: 50%;
    animation: energyPulse 2s ease-in-out infinite;
}

.energy-pulse::before {
    content: '⚡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    color: #fbbf24;
}

@keyframes energyPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
}

/* CO2 reduction indicators */
.co2-indicator {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    animation: co2Float 4s ease-in-out infinite;
}

.co2-indicator:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.co2-indicator:nth-child(2) {
    top: 30%;
    right: 20%;
    animation-delay: 1s;
}

.co2-indicator:nth-child(3) {
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

@keyframes co2Float {
    0%, 100% {
        transform: translateY(0px);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Mining farm activation effect */
.mining-farm {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
}

.mining-rig {
    width: 15px;
    height: 25px;
    background: linear-gradient(to top, #374151, #6b7280);
    border-radius: 2px;
    position: relative;
    animation: rigActivation 0.5s ease-in-out forwards;
}

.mining-rig::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 3px;
    background: #10b981;
    border-radius: 1px;
    opacity: 0;
    animation: rigLight 0.5s ease-in-out 0.5s forwards;
}

.mining-rig:nth-child(1) { animation-delay: 0s; }
.mining-rig:nth-child(2) { animation-delay: 0.2s; }
.mining-rig:nth-child(3) { animation-delay: 0.4s; }
.mining-rig:nth-child(4) { animation-delay: 0.6s; }
.mining-rig:nth-child(5) { animation-delay: 0.8s; }

@keyframes rigActivation {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes rigLight {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* Success message styling */
.success-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
    100% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    }
}

.success-details {
    font-size: 16px;
    margin: 15px 0;
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 1s forwards;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-animation-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.close-animation-btn:hover {
    background: white;
    color: #10b981;
}
