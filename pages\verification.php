<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireLogin();

$page_title = 'Account Verification';
$user_id = getCurrentUserId();
$user = getUserById($user_id);
$verification = getUserVerification($user_id);

// Check if KYC is required
$kyc_required = isKYCRequired();

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $first_name = sanitizeInput($_POST['first_name'] ?? '');
        $last_name = sanitizeInput($_POST['last_name'] ?? '');
        $birth_date = sanitizeInput($_POST['birth_date'] ?? '');
        $passport_file = $_FILES['passport_photo'] ?? null;
        
        // Validation
        if (empty($first_name) || empty($last_name) || empty($birth_date)) {
            $error_message = 'Please fill in all required fields';
        } elseif (!$passport_file || $passport_file['error'] !== 0) {
            $error_message = 'Please select a valid passport photo';
        } else {
            $result = submitVerificationRequest($user_id, $first_name, $last_name, $birth_date, $passport_file);
            
            if ($result['success']) {
                $success_message = $result['message'];
                // Refresh verification data
                $verification = getUserVerification($user_id);
                $user = getUserById($user_id); // Refresh user data
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Account Verification</h1>
            <p class="text-gray-600 mt-2">Complete your account verification to start investing</p>
        </div>

        <!-- Flash Messages -->
        <?php if ($error_message): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- KYC Disabled Notice -->
        <?php if (!$kyc_required): ?>
            <div class="bg-blue-50 border border-blue-200 text-blue-700 px-6 py-4 rounded-xl mb-6">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-3 text-xl"></i>
                    <div>
                        <h3 class="text-lg font-medium text-blue-800">Verification Not Required</h3>
                        <p class="text-blue-700 mt-1">Account verification is currently disabled by the administrator. You can use all platform features without verification.</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Verification Status Card -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100 mb-8">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Verification Status</h2>
                    <?php
                    $status = $user['verification_status'];
                    $statusColors = [
                        'unverified' => 'bg-gray-100 text-gray-800',
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'verified' => 'bg-green-100 text-green-800',
                        'rejected' => 'bg-red-100 text-red-800'
                    ];
                    $statusIcons = [
                        'unverified' => 'fas fa-clock',
                        'pending' => 'fas fa-hourglass-half',
                        'verified' => 'fas fa-check-circle',
                        'rejected' => 'fas fa-times-circle'
                    ];
                    ?>
                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $statusColors[$status]; ?>">
                        <i class="<?php echo $statusIcons[$status]; ?> mr-1"></i>
                        <?php echo ucfirst($status); ?>
                    </span>
                </div>
                
                <?php if ($status === 'unverified'): ?>
                    <p class="text-gray-600">Your account is not verified yet. Please submit your verification documents below.</p>
                <?php elseif ($status === 'pending'): ?>
                    <p class="text-yellow-700">Your verification is being reviewed by our team. This usually takes 24-48 hours.</p>
                <?php elseif ($status === 'verified'): ?>
                    <p class="text-green-700">Your account is verified! You can now make investments.</p>
                <?php elseif ($status === 'rejected'): ?>
                    <p class="text-red-700">Your verification was rejected. Please submit new documents.</p>
                    <?php if ($verification && $verification['admin_notes']): ?>
                        <div class="mt-3 p-3 bg-red-50 rounded-lg">
                            <p class="text-sm text-red-700"><strong>Admin Notes:</strong> <?php echo htmlspecialchars($verification['admin_notes']); ?></p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Verification Form -->
        <?php if ($status === 'unverified' || $status === 'rejected'): ?>
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Submit Verification Documents</h2>
                <p class="text-gray-600 mt-2">Please provide the following information to verify your account</p>
            </div>
            
            <form method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="first_name" name="first_name" required
                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="last_name" name="last_name" required
                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label for="birth_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Date of Birth <span class="text-red-500">*</span>
                    </label>
                    <input type="date" id="birth_date" name="birth_date" required
                           value="<?php echo htmlspecialchars($_POST['birth_date'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="passport_photo" class="block text-sm font-medium text-gray-700 mb-2">
                        Passport Photo <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-blue-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-3"></i>
                            <div class="flex text-sm text-gray-600">
                                <label for="passport_photo" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <span>Upload passport photo</span>
                                    <input id="passport_photo" name="passport_photo" type="file" accept="image/*" required class="sr-only" onchange="handleFileSelect(this)">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                            <div id="filePreview" class="hidden mt-4">
                                <div class="flex items-center justify-center">
                                    <div class="bg-blue-50 rounded-lg p-3">
                                        <i class="fas fa-file-image text-blue-500 mr-2"></i>
                                        <span id="fileName" class="text-sm text-blue-700"></span>
                                        <span id="fileSize" class="text-xs text-gray-500 ml-2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                        <div class="text-sm text-blue-700">
                            <p class="font-medium mb-1">Verification Requirements:</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li>Photo must be clear and readable</li>
                                <li>All information must be visible</li>
                                <li>Document must be valid and not expired</li>
                                <li>Photo should be in good lighting</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-paper-plane mr-2"></i>Submit Verification
                    </button>
                </div>
            </form>
        </div>
        <?php endif; ?>

        <!-- Verification History -->
        <?php if ($verification): ?>
        <div class="bg-white rounded-xl card-shadow border border-gray-100 mt-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Verification Details</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="text-sm text-gray-600">Name</p>
                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($verification['first_name'] . ' ' . $verification['last_name']); ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Date of Birth</p>
                        <p class="font-medium text-gray-900"><?php echo date('F j, Y', strtotime($verification['birth_date'])); ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Submitted</p>
                        <p class="font-medium text-gray-900"><?php echo date('F j, Y g:i A', strtotime($verification['submitted_at'])); ?></p>
                    </div>
                    <?php if ($verification['processed_at']): ?>
                    <div>
                        <p class="text-sm text-gray-600">Processed</p>
                        <p class="font-medium text-gray-900"><?php echo date('F j, Y g:i A', strtotime($verification['processed_at'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>

<script>
function handleFileSelect(input) {
    const file = input.files[0];
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    
    if (file) {
        fileName.textContent = file.name;
        fileSize.textContent = '(' + (file.size / 1024 / 1024).toFixed(2) + ' MB)';
        filePreview.classList.remove('hidden');
    } else {
        filePreview.classList.add('hidden');
    }
}
</script>

<?php include '../includes/footer.php'; ?>
