<?php
/**
 * Тест исправлений для AstroGenix
 * Проверка трех основных исправлений
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Тест исправлений AstroGenix</h1>";

// Подключаем файлы
try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    echo "<p>✅ Все файлы подключены успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения файлов: " . $e->getMessage() . "</p>";
    exit;
}

// Тестируем подключение к БД
try {
    $db = getDB();
    echo "<p>✅ Подключение к БД успешно</p>";
} catch (Exception $e) {
    echo "<p>❌ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>🔧 1. Тест исправления ошибки базы данных в админ-панели</h2>";

// Проверяем исправление в admin/users.php
if (file_exists('admin/users.php')) {
    $admin_users_content = file_get_contents('admin/users.php');
    
    // Проверяем, что старые упоминания user_investments заменены
    $old_table_count = substr_count($admin_users_content, 'user_investments');
    $new_table_count = substr_count($admin_users_content, 'user_mining_investments');
    
    if ($old_table_count === 0 && $new_table_count > 0) {
        echo "<p>✅ Таблица user_investments заменена на user_mining_investments</p>";
        echo "<p>✅ Найдено $new_table_count упоминаний правильной таблицы</p>";
    } else {
        echo "<p>❌ Найдено $old_table_count упоминаний старой таблицы user_investments</p>";
        echo "<p>⚠️ Найдено $new_table_count упоминаний новой таблицы user_mining_investments</p>";
    }
    
    // Тестируем SQL запрос
    try {
        $stmt = $db->prepare("
            SELECT u.*, 
                   COUNT(DISTINCT umi.id) as total_investments,
                   SUM(CASE WHEN umi.is_active = 1 THEN umi.amount ELSE 0 END) as total_invested,
                   COUNT(DISTINCT t.id) as total_transactions
            FROM users u
            LEFT JOIN user_mining_investments umi ON u.id = umi.user_id
            LEFT JOIN transactions t ON u.id = t.user_id
            WHERE u.is_admin = 0
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $test_user = $stmt->fetch();
        
        echo "<p>✅ SQL запрос в админ-панели работает корректно</p>";
        echo "<p>✅ Тестовый запрос выполнен без ошибок</p>";
        
    } catch (PDOException $e) {
        echo "<p>❌ Ошибка SQL запроса: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>❌ Файл admin/users.php не найден</p>";
}

echo "<h2>👥 2. Тест исправления названия раздела администрации</h2>";

// Проверяем исправление в admin/index.php
if (file_exists('admin/index.php')) {
    $admin_index_content = file_get_contents('admin/index.php');
    
    // Проверяем замену "Эко-майнеры" на "Пользователи"
    if (strpos($admin_index_content, 'Эко-майнеры') !== false) {
        echo "<p>❌ Найдено упоминание 'Эко-майнеры' в админ-панели</p>";
    } else {
        echo "<p>✅ Упоминание 'Эко-майнеры' удалено</p>";
    }
    
    if (strpos($admin_index_content, 'Пользователи') !== false) {
        echo "<p>✅ Найдено правильное название 'Пользователи'</p>";
    } else {
        echo "<p>❌ Название 'Пользователи' не найдено</p>";
    }
    
} else {
    echo "<p>❌ Файл admin/index.php не найден</p>";
}

// Проверяем заголовок в admin/users.php
if (file_exists('admin/users.php')) {
    $admin_users_content = file_get_contents('admin/users.php');
    
    if (strpos($admin_users_content, 'Управление пользователями') !== false) {
        echo "<p>✅ Заголовок 'Управление пользователями' найден</p>";
    } else {
        echo "<p>❌ Заголовок 'Управление пользователями' не найден</p>";
    }
}

echo "<h2>🤝 3. Тест реферальной программы в дашборде</h2>";

// Проверяем добавление реферальной секции в dashboard.php
if (file_exists('pages/dashboard.php')) {
    $dashboard_content = file_get_contents('pages/dashboard.php');
    
    $referral_features = [
        'Реферальная программа' => 'Заголовок реферальной секции',
        'referral-code' => 'Поле реферального кода',
        'referral-link' => 'Поле реферальной ссылки',
        'copyReferralCode' => 'Функция копирования кода',
        'copyReferralLink' => 'Функция копирования ссылки',
        'shareToTelegram' => 'Функция поделиться в Telegram',
        'shareToWhatsApp' => 'Функция поделиться в WhatsApp',
        'getReferralStats' => 'Получение статистики рефералов',
        'getMyReferrals' => 'Получение списка рефералов'
    ];
    
    foreach ($referral_features as $feature => $description) {
        if (strpos($dashboard_content, $feature) !== false) {
            echo "<p>✅ $description найден</p>";
        } else {
            echo "<p>❌ $description НЕ найден</p>";
        }
    }
    
    // Проверяем функции в includes/functions.php
    if (function_exists('getReferralStats')) {
        echo "<p>✅ Функция getReferralStats() доступна</p>";
    } else {
        echo "<p>❌ Функция getReferralStats() НЕ найдена</p>";
    }
    
    if (function_exists('getMyReferrals')) {
        echo "<p>✅ Функция getMyReferrals() доступна</p>";
    } else {
        echo "<p>❌ Функция getMyReferrals() НЕ найдена</p>";
    }
    
} else {
    echo "<p>❌ Файл pages/dashboard.php не найден</p>";
}

echo "<h2>🧪 4. Тест функциональности реферальной системы</h2>";

// Тестируем реферальные функции
try {
    // Создаем тестового пользователя для проверки
    $test_user_id = 1; // Предполагаем, что есть пользователь с ID 1
    
    // Тестируем получение статистики рефералов
    $referral_stats = getReferralStats($test_user_id);
    echo "<p>✅ Статистика рефералов получена:</p>";
    echo "<ul>";
    echo "<li>Всего рефералов: " . ($referral_stats['total_referrals'] ?? 0) . "</li>";
    echo "<li>Всего заработано: " . formatCurrency($referral_stats['total_earnings'] ?? 0) . "</li>";
    echo "<li>Активных рефералов: " . ($referral_stats['active_referrals'] ?? 0) . "</li>";
    echo "</ul>";
    
    // Тестируем получение списка рефералов
    $my_referrals = getMyReferrals($test_user_id);
    echo "<p>✅ Список рефералов получен: " . count($my_referrals) . " рефералов</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Ошибка тестирования реферальных функций: " . $e->getMessage() . "</p>";
}

echo "<h2>🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ ИСПРАВЛЕНИЙ</h2>";

$all_fixes_ready = true;
$issues = [];

// Проверяем ключевые исправления
if (file_exists('admin/users.php')) {
    $admin_content = file_get_contents('admin/users.php');
    if (strpos($admin_content, 'user_investments') !== false) {
        $all_fixes_ready = false;
        $issues[] = "Не все упоминания user_investments заменены в админ-панели";
    }
}

if (file_exists('admin/index.php')) {
    $admin_index = file_get_contents('admin/index.php');
    if (strpos($admin_index, 'Эко-майнеры') !== false) {
        $all_fixes_ready = false;
        $issues[] = "Название 'Эко-майнеры' не заменено на 'Пользователи'";
    }
}

if (file_exists('pages/dashboard.php')) {
    $dashboard = file_get_contents('pages/dashboard.php');
    if (strpos($dashboard, 'Реферальная программа') === false) {
        $all_fixes_ready = false;
        $issues[] = "Реферальная программа не добавлена в дашборд";
    }
}

if ($all_fixes_ready && empty($issues)) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 2px solid #28a745; color: #155724; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);'>";
    echo "<h3 style='margin: 0 0 15px 0; font-size: 24px;'>🎉 ВСЕ ИСПРАВЛЕНИЯ УСПЕШНО ПРИМЕНЕНЫ!</h3>";
    echo "<p><strong>✅ Исправленные проблемы:</strong></p>";
    echo "<ul style='margin: 10px 0;'>";
    echo "<li>🔧 <strong>Ошибка базы данных</strong> - таблица user_investments заменена на user_mining_investments</li>";
    echo "<li>👥 <strong>Название раздела</strong> - 'Эко-майнеры' заменено на 'Пользователи'</li>";
    echo "<li>🤝 <strong>Реферальная программа</strong> - добавлена в пользовательский дашборд</li>";
    echo "</ul>";
    echo "<p><strong>🚀 Новые возможности в дашборде:</strong></p>";
    echo "<ul style='margin: 10px 0;'>";
    echo "<li>📊 Статистика рефералов (количество и заработок)</li>";
    echo "<li>🔗 Реферальный код и ссылка с кнопками копирования</li>";
    echo "<li>📱 Быстрое поделиться в Telegram и WhatsApp</li>";
    echo "<li>👥 Список последних рефералов с заработком</li>";
    echo "<li>🎨 Красивый дизайн в зелено-фиолетовой гамме</li>";
    echo "</ul>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;'>";
    echo "<a href='pages/dashboard.php' target='_blank' style='background: #28a745; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>📊 Дашборд с рефералами</a>";
    echo "<a href='admin/users.php' target='_blank' style='background: #6f42c1; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>👥 Управление пользователями</a>";
    echo "<a href='admin/index.php' target='_blank' style='background: #fd7e14; color: white; padding: 10px; border-radius: 8px; text-decoration: none; text-align: center;'>🏠 Админ-панель</a>";
    echo "</div>";
    echo "<p style='margin: 15px 0 0 0; font-weight: bold; color: #0f5132;'>🌱 Все проблемы решены, платформа работает стабильно!</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ Обнаружены проблемы</h3>";
    echo "<p><strong>Найденные проблемы:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "<p><strong>Рекомендации:</strong></p>";
    echo "<ul>";
    echo "<li>Проверьте правильность замены названий таблиц</li>";
    echo "<li>Убедитесь в корректности обновления файлов</li>";
    echo "<li>Проверьте права доступа к файлам</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr style='margin: 30px 0; border: 2px solid #28a745;'>";
echo "<div style='text-align: center; background: #f8f9fa; padding: 15px; border-radius: 10px;'>";
echo "<p style='margin: 0; font-weight: bold; color: #495057;'>🔧 AstroGenix Platform - Исправления v1.0</p>";
echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>Время тестирования: " . date('Y-m-d H:i:s') . " | Все исправления протестированы</p>";
echo "</div>";
?>
